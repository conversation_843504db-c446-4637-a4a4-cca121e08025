const fs = require('fs');
const path = require('path');
const db = require('./backend/config/db.config');

// Create directory if it doesn't exist
const imagesDir = path.join(__dirname, 'backend/public/images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Function to create a simple SVG placeholder image
function createPlaceholderSVG(filename, text, bgColor) {
  const svg = `<svg xmlns="http://www.w3.org/2000/svg" width="500" height="300" viewBox="0 0 500 300">
    <rect width="500" height="300" fill="${bgColor}" />
    <text x="50%" y="50%" font-family="Arial" font-size="24" fill="white" text-anchor="middle" dominant-baseline="middle">${text}</text>
  </svg>`;
  
  const filePath = path.join(imagesDir, filename);
  fs.writeFileSync(filePath, svg);
  console.log(`Created ${filename}`);
  return filename;
}

// Function to update product images in the database
async function updateProductImages(imageFilenames) {
  try {
    // Get all products
    const products = await db.query('SELECT id FROM products ORDER BY id');
    
    // Update each product with an image
    for (let i = 0; i < Math.min(products.length, imageFilenames.length); i++) {
      const imagePath = `/images/${imageFilenames[i]}`;
      await db.query('UPDATE products SET image = ? WHERE id = ?', [imagePath, products[i].id]);
      console.log(`Updated product ${products[i].id} with image ${imagePath}`);
    }
  } catch (err) {
    console.error('Error updating product images:', err);
  }
}

// Function to update blog images in the database
async function updateBlogImages(imageFilenames) {
  try {
    // Get all blogs
    const blogs = await db.query('SELECT id FROM blogs ORDER BY id');
    
    // Update each blog with an image
    for (let i = 0; i < Math.min(blogs.length, imageFilenames.length); i++) {
      const imagePath = `/images/${imageFilenames[i]}`;
      await db.query('UPDATE blogs SET image = ? WHERE id = ?', [imagePath, blogs[i].id]);
      console.log(`Updated blog ${blogs[i].id} with image ${imagePath}`);
    }
  } catch (err) {
    console.error('Error updating blog images:', err);
  }
}

// Main function
async function main() {
  try {
    // Create product images
    const productImageFilenames = [];
    const productColors = ['#3498db', '#e74c3c', '#2ecc71'];
    
    for (let i = 0; i < 3; i++) {
      const filename = `product-${i+1}.svg`;
      createPlaceholderSVG(filename, `Product ${i+1}`, productColors[i]);
      productImageFilenames.push(filename);
    }
    
    // Create blog images
    const blogImageFilenames = [];
    const blogColors = ['#9b59b6', '#f39c12'];
    
    for (let i = 0; i < 2; i++) {
      const filename = `blog-${i+1}.svg`;
      createPlaceholderSVG(filename, `Blog Post ${i+1}`, blogColors[i]);
      blogImageFilenames.push(filename);
    }
    
    // Update database records
    await updateProductImages(productImageFilenames);
    await updateBlogImages(blogImageFilenames);
    
    console.log('All images created and database updated successfully!');
    process.exit(0);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

// Run the main function
main();
