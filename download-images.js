const fs = require('fs');
const path = require('path');
const https = require('https');
const db = require('./backend/config/db.config');

// Create directory if it doesn't exist
const imagesDir = path.join(__dirname, 'backend/public/images');
if (!fs.existsSync(imagesDir)) {
  fs.mkdirSync(imagesDir, { recursive: true });
}

// Sample image URLs (placeholder images)
const productImages = [
  'https://via.placeholder.com/500x300/3498db/ffffff?text=Product+1',
  'https://via.placeholder.com/500x300/e74c3c/ffffff?text=Product+2',
  'https://via.placeholder.com/500x300/2ecc71/ffffff?text=Product+3'
];

const blogImages = [
  'https://via.placeholder.com/800x400/9b59b6/ffffff?text=Blog+Post+1',
  'https://via.placeholder.com/800x400/f39c12/ffffff?text=Blog+Post+2'
];

// Function to download an image
function downloadImage(url, filename) {
  return new Promise((resolve, reject) => {
    const filePath = path.join(imagesDir, filename);
    const file = fs.createWriteStream(filePath);
    
    https.get(url, (response) => {
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded ${filename}`);
        resolve(filename);
      });
    }).on('error', (err) => {
      fs.unlink(filePath, () => {}); // Delete the file if there's an error
      reject(err);
    });
  });
}

// Function to update product images in the database
async function updateProductImages(imageFilenames) {
  try {
    // Get all products
    const products = await db.query('SELECT id FROM products ORDER BY id');
    
    // Update each product with an image
    for (let i = 0; i < Math.min(products.length, imageFilenames.length); i++) {
      const imagePath = `/images/${imageFilenames[i]}`;
      await db.query('UPDATE products SET image = ? WHERE id = ?', [imagePath, products[i].id]);
      console.log(`Updated product ${products[i].id} with image ${imagePath}`);
    }
  } catch (err) {
    console.error('Error updating product images:', err);
  }
}

// Function to update blog images in the database
async function updateBlogImages(imageFilenames) {
  try {
    // Get all blogs
    const blogs = await db.query('SELECT id FROM blogs ORDER BY id');
    
    // Update each blog with an image
    for (let i = 0; i < Math.min(blogs.length, imageFilenames.length); i++) {
      const imagePath = `/images/${imageFilenames[i]}`;
      await db.query('UPDATE blogs SET image = ? WHERE id = ?', [imagePath, blogs[i].id]);
      console.log(`Updated blog ${blogs[i].id} with image ${imagePath}`);
    }
  } catch (err) {
    console.error('Error updating blog images:', err);
  }
}

// Main function
async function main() {
  try {
    // Download product images
    const productImageFilenames = [];
    for (let i = 0; i < productImages.length; i++) {
      const filename = `product-${i+1}.jpg`;
      await downloadImage(productImages[i], filename);
      productImageFilenames.push(filename);
    }
    
    // Download blog images
    const blogImageFilenames = [];
    for (let i = 0; i < blogImages.length; i++) {
      const filename = `blog-${i+1}.jpg`;
      await downloadImage(blogImages[i], filename);
      blogImageFilenames.push(filename);
    }
    
    // Update database records
    await updateProductImages(productImageFilenames);
    await updateBlogImages(blogImageFilenames);
    
    console.log('All images downloaded and database updated successfully!');
    process.exit(0);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

// Run the main function
main();
