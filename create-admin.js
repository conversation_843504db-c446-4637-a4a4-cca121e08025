const bcrypt = require('bcrypt');
const db = require('./backend/config/db.config');

async function createAdminUser() {
  try {
    console.log('Checking if admin user exists...');
    
    // Check if admin user already exists
    const checkSql = 'SELECT * FROM users WHERE email = ?';
    const existingUsers = await db.query(checkSql, ['<EMAIL>']);
    
    if (existingUsers.length > 0) {
      console.log('Admin user already exists. Updating password...');
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      // Update the admin user's password
      const updateSql = 'UPDATE users SET password = ? WHERE email = ?';
      await db.query(updateSql, [hashedPassword, '<EMAIL>']);
      
      console.log('Admin password updated successfully!');
    } else {
      console.log('Admin user does not exist. Creating new admin user...');
      
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('admin123', salt);
      
      // Create the admin user
      const insertSql = `
        INSERT INTO users (name, email, password, role)
        VALUES (?, ?, ?, ?)
      `;
      
      await db.query(insertSql, [
        'Admin User',
        '<EMAIL>',
        hashedPassword,
        'admin'
      ]);
      
      console.log('Admin user created successfully!');
    }
    
    console.log('\nAdmin User Details:');
    console.log('Email: <EMAIL>');
    console.log('Password: admin123');
    console.log('Role: admin');
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating admin user:', error);
    process.exit(1);
  }
}

createAdminUser();
