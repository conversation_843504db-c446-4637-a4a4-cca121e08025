const axios = require('axios');

const API_URL = 'http://localhost:5000/api';

// Create axios instance
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Store the auth token
let authToken = '';

// Test functions
const tests = {
  // Test health endpoint
  testHealth: async () => {
    try {
      console.log('\n--- Testing Health Endpoint ---');
      const response = await api.get('/health');
      console.log('Health check successful:', response.data);
      return true;
    } catch (error) {
      console.error('Health check failed:', error.message);
      return false;
    }
  },

  // Test user registration
  testRegister: async () => {
    try {
      console.log('\n--- Testing User Registration ---');
      const userData = {
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        password: 'password123'
      };
      
      console.log('Registering user:', userData.email);
      const response = await api.post('/auth/register', userData);
      
      console.log('Registration successful:', response.data.message);
      authToken = response.data.token;
      console.log('Auth token received');
      
      return true;
    } catch (error) {
      console.error('Registration failed:', error.response?.data?.message || error.message);
      return false;
    }
  },

  // Test user login
  testLogin: async () => {
    try {
      console.log('\n--- Testing User Login ---');
      const loginData = {
        email: '<EMAIL>',
        password: 'admin123'
      };
      
      console.log('Logging in as:', loginData.email);
      const response = await api.post('/auth/login', loginData);
      
      console.log('Login successful:', response.data.message);
      authToken = response.data.token;
      console.log('Auth token received');
      
      // Set the token for subsequent requests
      api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`;
      
      return true;
    } catch (error) {
      console.error('Login failed:', error.response?.data?.message || error.message);
      return false;
    }
  },

  // Test get user profile
  testGetProfile: async () => {
    try {
      console.log('\n--- Testing Get User Profile ---');
      const response = await api.get('/auth/profile');
      console.log('Profile retrieved successfully:', response.data.user);
      return true;
    } catch (error) {
      console.error('Get profile failed:', error.response?.data?.message || error.message);
      return false;
    }
  },

  // Test get all products
  testGetProducts: async () => {
    try {
      console.log('\n--- Testing Get All Products ---');
      const response = await api.get('/products');
      console.log(`Retrieved ${response.data.products.length} products`);
      console.log('First product:', response.data.products[0]);
      return true;
    } catch (error) {
      console.error('Get products failed:', error.response?.data?.message || error.message);
      return false;
    }
  },

  // Test get all blogs
  testGetBlogs: async () => {
    try {
      console.log('\n--- Testing Get All Blogs ---');
      const response = await api.get('/blogs');
      console.log(`Retrieved ${response.data.blogs.length} blog posts`);
      if (response.data.blogs.length > 0) {
        console.log('First blog post:', response.data.blogs[0]);
      }
      return true;
    } catch (error) {
      console.error('Get blogs failed:', error.response?.data?.message || error.message);
      return false;
    }
  }
};

// Run all tests
const runTests = async () => {
  console.log('=== Starting API Tests ===');
  
  // Test health endpoint
  await tests.testHealth();
  
  // Test authentication
  const loginSuccess = await tests.testLogin();
  if (!loginSuccess) {
    console.log('Trying to register a new user...');
    await tests.testRegister();
  }
  
  // Test profile
  await tests.testGetProfile();
  
  // Test products
  await tests.testGetProducts();
  
  // Test blogs
  await tests.testGetBlogs();
  
  console.log('\n=== API Tests Completed ===');
};

// Run the tests
runTests();
