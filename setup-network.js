const os = require('os');
const fs = require('fs');
const path = require('path');

// Get the local IP address
function getLocalIpAddress() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  Object.keys(interfaces).forEach((interfaceName) => {
    const interfaceInfo = interfaces[interfaceName];
    interfaceInfo.forEach((info) => {
      // Skip internal and non-IPv4 addresses
      if (!info.internal && info.family === 'IPv4') {
        addresses.push(info.address);
      }
    });
  });
  
  return addresses[0] || 'localhost'; // Return the first IP address or localhost as fallback
}

// Update the React frontend .env file
function updateReactEnv(ipAddress) {
  const envPath = path.join(__dirname, 'react-frontend', '.env');
  
  if (fs.existsSync(envPath)) {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Update the API URL
    envContent = envContent.replace(
      /REACT_APP_API_URL=.*/,
      `REACT_APP_API_URL=http://${ipAddress}:5000/api`
    );
    
    fs.writeFileSync(envPath, envContent);
    console.log(`Updated React frontend .env with API URL: http://${ipAddress}:5000/api`);
  } else {
    const envContent = `PORT=3000
HOST=0.0.0.0
# Use IP address instead of localhost for cross-device access
REACT_APP_API_URL=http://${ipAddress}:5000/api
`;
    fs.writeFileSync(envPath, envContent);
    console.log(`Created React frontend .env with API URL: http://${ipAddress}:5000/api`);
  }
}

// Update the backend .env file
function updateBackendEnv(ipAddress) {
  const envPath = path.join(__dirname, '.env');
  
  if (fs.existsSync(envPath)) {
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    // Update the FRONTEND_URL
    if (envContent.includes('FRONTEND_URL=')) {
      envContent = envContent.replace(
        /FRONTEND_URL=.*/,
        `FRONTEND_URL=http://localhost:3000,http://${ipAddress}:3000`
      );
    } else {
      envContent += `\n# Frontend URL for CORS (comma-separated list for multiple origins)
FRONTEND_URL=http://localhost:3000,http://${ipAddress}:3000\n`;
    }
    
    fs.writeFileSync(envPath, envContent);
    console.log(`Updated backend .env with FRONTEND_URL: http://localhost:3000,http://${ipAddress}:3000`);
  }
}

// Main function
function main() {
  const ipAddress = getLocalIpAddress();
  console.log(`\nDetected local IP address: ${ipAddress}\n`);
  
  updateReactEnv(ipAddress);
  updateBackendEnv(ipAddress);
  
  console.log(`\nConfiguration complete!`);
  console.log(`\nYou can now access your application from other devices on your network:`);
  console.log(`- Frontend: http://${ipAddress}:3000`);
  console.log(`- Backend API: http://${ipAddress}:5000/api`);
  console.log(`\nMake sure to restart both the frontend and backend servers for the changes to take effect.`);
}

// Run the main function
main();
