const os = require('os');
const interfaces = os.networkInterfaces();
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Get the port from environment or use default
const port = process.env.PORT || 3000;

// Find IP addresses
const addresses = [];
Object.keys(interfaces).forEach((interfaceName) => {
  const interfaceInfo = interfaces[interfaceName];
  interfaceInfo.forEach((info) => {
    // Skip internal and non-IPv4 addresses
    if (!info.internal && info.family === 'IPv4') {
      addresses.push(info.address);
    }
  });
});

console.log('\n=== Network Addresses for Your React App ===\n');

if (addresses.length > 0) {
  console.log('Your React app is running at:');
  console.log(`Local:            http://localhost:${port}`);
  
  addresses.forEach((address) => {
    console.log(`On Your Network:  http://${address}:${port}`);
  });
} else {
  console.log('No network interfaces found. Your app is running at:');
  console.log(`Local:            http://localhost:${port}`);
}

console.log('\nYou can access your React app from other devices on your network using the "On Your Network" address.');
console.log('\n===========================================\n');
