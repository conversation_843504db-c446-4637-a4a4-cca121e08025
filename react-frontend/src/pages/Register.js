import React, { useState, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const Register = () => {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [formError, setFormError] = useState('');

  const { register, loading, error } = useContext(AuthContext);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Form validation
    if (!name || !email || !password || !confirmPassword) {
      setFormError('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      setFormError('Passwords do not match');
      return;
    }

    if (password.length < 6) {
      setFormError('Password must be at least 6 characters long');
      return;
    }

    try {
      await register(name, email, password);
      navigate('/profile'); // Redirect to profile page after successful registration
    } catch (err) {
      console.error('Registration error:', err);
      // Error is handled by the AuthContext
    }
  };

  return (
    <div className="uk-section uk-section-muted uk-flex uk-flex-middle uk-animation-fade" uk-height-viewport="true">
      <div className="uk-width-1-1">
        <div className="uk-container">
          <div className="uk-grid-margin uk-grid uk-grid-stack" uk-grid="true">
            <div className="uk-width-1-1@m">
              <div className="uk-margin uk-width-large uk-margin-auto uk-card uk-card-default uk-card-body uk-box-shadow-large">
                <h3 className="uk-card-title uk-text-center">Create Account</h3>

                {error && (
                  <div className="uk-alert-danger" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{error}</p>
                  </div>
                )}

                {formError && (
                  <div className="uk-alert-danger" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{formError}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="uk-margin">
                    <div className="uk-inline uk-width-1-1">
                      <span className="uk-form-icon" uk-icon="icon: user"></span>
                      <input
                        className="uk-input uk-form-large"
                        type="text"
                        id="name"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Full Name"
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <div className="uk-inline uk-width-1-1">
                      <span className="uk-form-icon" uk-icon="icon: mail"></span>
                      <input
                        className="uk-input uk-form-large"
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Email Address"
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <div className="uk-inline uk-width-1-1">
                      <span className="uk-form-icon" uk-icon="icon: lock"></span>
                      <input
                        className="uk-input uk-form-large"
                        type="password"
                        id="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Password"
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <div className="uk-inline uk-width-1-1">
                      <span className="uk-form-icon" uk-icon="icon: check"></span>
                      <input
                        className="uk-input uk-form-large"
                        type="password"
                        id="confirmPassword"
                        value={confirmPassword}
                        onChange={(e) => setConfirmPassword(e.target.value)}
                        placeholder="Confirm Password"
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <button
                      className="uk-button uk-button-primary uk-button-large uk-width-1-1"
                      type="submit"
                      disabled={loading}
                    >
                      {loading ? (
                        <span>
                          <div uk-spinner="ratio: 0.8"></div> Registering...
                        </span>
                      ) : 'Register'}
                    </button>
                  </div>
                </form>

                <hr className="uk-divider-icon" />

                <div className="uk-text-center">
                  <p className="uk-text-small">Already have an account?</p>
                  <Link to="/login" className="uk-button uk-button-default uk-width-1-1">Login</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
