import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import productService from '../../services/productService';

const ProductForm = () => {
  const { id } = useParams();
  const isEditMode = !!id;
  const { user, loading } = useContext(AuthContext);
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    stock: ''
  });

  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    // Redirect if not admin
    if (!loading && (!user || user.role !== 'admin')) {
      navigate('/login');
    }

    // Fetch product data if in edit mode
    const fetchProduct = async () => {
      try {
        setIsLoading(true);
        const data = await productService.getProductById(id);
        const product = data.product;

        setFormData({
          name: product.name,
          description: product.description,
          price: product.price,
          category: product.category,
          stock: product.stock
        });

        if (product.image) {
          setImagePreview(`http://localhost:5000${product.image}`);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to fetch product data');
        setIsLoading(false);
      }
    };

    if (isEditMode && user && user.role === 'admin') {
      fetchProduct();
    }
  }, [id, isEditMode, user, loading, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setIsLoading(true);
      setError(null);

      // Prepare form data for API
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        stock: parseInt(formData.stock)
      };

      if (imageFile) {
        productData.image = imageFile;
      }

      if (isEditMode) {
        await productService.updateProduct(id, productData);
        setSuccess('Product updated successfully');
      } else {
        await productService.createProduct(productData);
        setSuccess('Product created successfully');

        // Reset form after successful creation
        setFormData({
          name: '',
          description: '',
          price: '',
          category: '',
          stock: ''
        });
        setImageFile(null);
        setImagePreview('');
      }

      setIsLoading(false);

      // Redirect after a short delay
      setTimeout(() => {
        navigate('/admin/products');
      }, 2000);
    } catch (err) {
      console.error('Error saving product:', err);
      setError(err.response?.data?.message || 'Failed to save product');
      setIsLoading(false);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>Access denied. You must be an admin to view this page.</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="uk-heading-divider">{isEditMode ? 'Edit Product' : 'Add New Product'}</h1>

      {error && (
        <div className="uk-alert-danger" uk-alert="true">
          <button className="uk-alert-close" uk-close="true" onClick={() => setError(null)} aria-label="Close"></button>
          <p>{error}</p>
        </div>
      )}

      {success && (
        <div className="uk-alert-success" uk-alert="true">
          <button className="uk-alert-close" uk-close="true" onClick={() => setSuccess(null)} aria-label="Close"></button>
          <p>{success}</p>
        </div>
      )}

      <div className="uk-card uk-card-default uk-card-body">
        <form onSubmit={handleSubmit} className="uk-form-stacked">
          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="name">Product Name</label>
            <div className="uk-form-controls">
              <input
                className="uk-input"
                id="name"
                name="name"
                type="text"
                placeholder="Enter product name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="description">Description</label>
            <div className="uk-form-controls">
              <textarea
                className="uk-textarea"
                id="description"
                name="description"
                rows="5"
                placeholder="Enter product description"
                value={formData.description}
                onChange={handleChange}
                required
              ></textarea>
            </div>
          </div>

          <div className="uk-grid-small" uk-grid="true">
            <div className="uk-width-1-3@s">
              <label className="uk-form-label" htmlFor="price">Price ($)</label>
              <div className="uk-form-controls">
                <input
                  className="uk-input"
                  id="price"
                  name="price"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  value={formData.price}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="uk-width-1-3@s">
              <label className="uk-form-label" htmlFor="category">Category</label>
              <div className="uk-form-controls">
                <select
                  className="uk-select"
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select Category</option>
                  <option value="Category 1">Category 1</option>
                  <option value="Category 2">Category 2</option>
                </select>
              </div>
            </div>

            <div className="uk-width-1-3@s">
              <label className="uk-form-label" htmlFor="stock">Stock</label>
              <div className="uk-form-controls">
                <input
                  className="uk-input"
                  id="stock"
                  name="stock"
                  type="number"
                  min="0"
                  placeholder="0"
                  value={formData.stock}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>

          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="image">Product Image</label>

            {imagePreview && (
              <div className="uk-margin-small">
                <img
                  src={imagePreview}
                  alt="Product preview"
                  className="uk-border-rounded"
                  style={{ maxHeight: '200px' }}
                />
              </div>
            )}

            <div className="uk-form-controls">
              <div uk-form-custom="target: true">
                <input
                  type="file"
                  id="image"
                  name="image"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                <input
                  className="uk-input uk-form-width-medium"
                  type="text"
                  placeholder="Select file"
                  disabled
                />
                <button className="uk-button uk-button-default" type="button" tabIndex="-1">Select</button>
              </div>
            </div>
          </div>

          <div className="uk-margin uk-text-right">
            <Link to="/admin/products" className="uk-button uk-button-default uk-margin-right">Cancel</Link>
            <button type="submit" className="uk-button uk-button-primary">
              {isEditMode ? 'Update Product' : 'Create Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductForm;
