import React, { useContext, useEffect, useState } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import blogService from '../../services/blogService';

const BlogForm = () => {
  const { id } = useParams();
  const isEditMode = !!id;
  const { user, loading } = useContext(AuthContext);
  const navigate = useNavigate();
  
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    status: 'published'
  });
  
  const [imageFile, setImageFile] = useState(null);
  const [imagePreview, setImagePreview] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    // Redirect if not admin
    if (!loading && (!user || user.role !== 'admin')) {
      navigate('/login');
    }
    
    // Fetch blog data if in edit mode
    const fetchBlog = async () => {
      try {
        setIsLoading(true);
        const data = await blogService.getBlogById(id);
        const blog = data.blog;
        
        setFormData({
          title: blog.title,
          content: blog.content,
          status: blog.status || 'published'
        });
        
        if (blog.image) {
          setImagePreview(`http://localhost:5000${blog.image}`);
        }
        
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching blog:', err);
        setError('Failed to fetch blog data');
        setIsLoading(false);
      }
    };
    
    if (isEditMode && user && user.role === 'admin') {
      fetchBlog();
    }
  }, [id, isEditMode, user, loading, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Prepare form data for API
      const blogData = {
        ...formData
      };
      
      if (imageFile) {
        blogData.image = imageFile;
      }
      
      let result;
      if (isEditMode) {
        result = await blogService.updateBlog(id, blogData);
        setSuccess('Blog post updated successfully');
      } else {
        result = await blogService.createBlog(blogData);
        setSuccess('Blog post created successfully');
        
        // Reset form after successful creation
        setFormData({
          title: '',
          content: '',
          status: 'published'
        });
        setImageFile(null);
        setImagePreview('');
      }
      
      setIsLoading(false);
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/admin/blogs');
      }, 2000);
    } catch (err) {
      console.error('Error saving blog:', err);
      setError(err.response?.data?.message || 'Failed to save blog post');
      setIsLoading(false);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>Access denied. You must be an admin to view this page.</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="uk-heading-divider">{isEditMode ? 'Edit Blog Post' : 'Create New Blog Post'}</h1>
      
      {error && (
        <div className="uk-alert-danger" uk-alert="true">
          <a className="uk-alert-close" uk-close="true" onClick={() => setError(null)}></a>
          <p>{error}</p>
        </div>
      )}
      
      {success && (
        <div className="uk-alert-success" uk-alert="true">
          <a className="uk-alert-close" uk-close="true" onClick={() => setSuccess(null)}></a>
          <p>{success}</p>
        </div>
      )}
      
      <div className="uk-card uk-card-default uk-card-body">
        <form onSubmit={handleSubmit} className="uk-form-stacked">
          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="title">Title</label>
            <div className="uk-form-controls">
              <input
                className="uk-input"
                id="title"
                name="title"
                type="text"
                placeholder="Enter blog title"
                value={formData.title}
                onChange={handleChange}
                required
              />
            </div>
          </div>
          
          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="content">Content</label>
            <div className="uk-form-controls">
              <textarea
                className="uk-textarea"
                id="content"
                name="content"
                rows="10"
                placeholder="Enter blog content"
                value={formData.content}
                onChange={handleChange}
                required
              ></textarea>
            </div>
          </div>
          
          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="status">Status</label>
            <div className="uk-form-controls">
              <select
                className="uk-select"
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
              >
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>
          </div>
          
          <div className="uk-margin">
            <label className="uk-form-label" htmlFor="image">Featured Image</label>
            
            {imagePreview && (
              <div className="uk-margin-small">
                <img 
                  src={imagePreview} 
                  alt="Blog preview" 
                  className="uk-border-rounded" 
                  style={{ maxHeight: '200px' }}
                />
              </div>
            )}
            
            <div className="uk-form-controls">
              <div uk-form-custom="target: true">
                <input
                  type="file"
                  id="image"
                  name="image"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                <input 
                  className="uk-input uk-form-width-medium" 
                  type="text" 
                  placeholder="Select file" 
                  disabled 
                />
                <button className="uk-button uk-button-default" type="button" tabIndex="-1">Select</button>
              </div>
            </div>
          </div>
          
          <div className="uk-margin uk-text-right">
            <Link to="/admin/blogs" className="uk-button uk-button-default uk-margin-right">Cancel</Link>
            <button type="submit" className="uk-button uk-button-primary">
              {isEditMode ? 'Update Blog Post' : 'Publish Blog Post'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BlogForm;
