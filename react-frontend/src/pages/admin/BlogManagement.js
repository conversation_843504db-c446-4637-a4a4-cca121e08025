import React, { useContext, useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import blogService from '../../services/blogService';

const BlogManagement = () => {
  const { user, loading } = useContext(AuthContext);
  const navigate = useNavigate();
  const [blogs, setBlogs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedBlogs, setSelectedBlogs] = useState([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    // Redirect if not admin
    if (!loading && (!user || user.role !== 'admin')) {
      navigate('/login');
    }
    
    // Fetch blogs
    const fetchBlogs = async () => {
      try {
        setIsLoading(true);
        const data = await blogService.getAllBlogs();
        setBlogs(data.blogs);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching blogs:', err);
        setError('Failed to fetch blog posts');
        setIsLoading(false);
      }
    };
    
    if (user && user.role === 'admin') {
      fetchBlogs();
    }
  }, [user, loading, navigate]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filteredBlogs = blogs.filter(blog => 
    blog.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    blog.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectBlog = (id) => {
    if (selectedBlogs.includes(id)) {
      setSelectedBlogs(selectedBlogs.filter(blogId => blogId !== id));
    } else {
      setSelectedBlogs([...selectedBlogs, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedBlogs.length === filteredBlogs.length) {
      setSelectedBlogs([]);
    } else {
      setSelectedBlogs(filteredBlogs.map(blog => blog.id));
    }
  };

  const handleDeleteSelected = async () => {
    try {
      for (const id of selectedBlogs) {
        await blogService.deleteBlog(id);
      }
      
      // Refresh blogs
      const data = await blogService.getAllBlogs();
      setBlogs(data.blogs);
      setSelectedBlogs([]);
      setShowDeleteConfirm(false);
    } catch (err) {
      console.error('Error deleting blogs:', err);
      setError('Failed to delete blog posts');
    }
  };

  if (loading || isLoading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>Access denied. You must be an admin to view this page.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className="uk-flex uk-flex-between uk-flex-middle">
        <h1 className="uk-heading-divider">Blog Management</h1>
        <Link to="/admin/blogs/new" className="uk-button uk-button-primary">
          <span uk-icon="icon: plus"></span> Add Blog Post
        </Link>
      </div>
      
      <div className="uk-margin">
        <div className="uk-card uk-card-default uk-card-body uk-padding-small">
          <div className="uk-grid-small" uk-grid="true">
            <div className="uk-width-expand">
              <div className="uk-inline uk-width-1-1">
                <span className="uk-form-icon" uk-icon="icon: search"></span>
                <input 
                  className="uk-input" 
                  type="text" 
                  placeholder="Search blog posts..." 
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
            </div>
            <div className="uk-width-auto">
              <button 
                className="uk-button uk-button-danger"
                disabled={selectedBlogs.length === 0}
                onClick={() => setShowDeleteConfirm(true)}
              >
                <span uk-icon="icon: trash"></span> Delete
              </button>
            </div>
          </div>
          
          <div className="uk-overflow-auto uk-margin-small-top">
            <table className="uk-table uk-table-divider uk-table-hover uk-table-small uk-table-middle">
              <thead>
                <tr>
                  <th className="uk-table-shrink">
                    <input 
                      className="uk-checkbox" 
                      type="checkbox" 
                      checked={selectedBlogs.length === filteredBlogs.length && filteredBlogs.length > 0}
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th className="uk-table-shrink">Image</th>
                  <th>Title</th>
                  <th>Author</th>
                  <th>Status</th>
                  <th>Date</th>
                  <th className="uk-table-shrink">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredBlogs.length > 0 ? (
                  filteredBlogs.map(blog => (
                    <tr key={blog.id}>
                      <td>
                        <input 
                          className="uk-checkbox" 
                          type="checkbox" 
                          checked={selectedBlogs.includes(blog.id)}
                          onChange={() => handleSelectBlog(blog.id)}
                        />
                      </td>
                      <td>
                        {blog.image ? (
                          <img 
                            src={`http://localhost:5000${blog.image}`} 
                            alt={blog.title} 
                            width="50" 
                            height="50"
                            className="uk-border-rounded"
                          />
                        ) : (
                          <div 
                            className="uk-placeholder uk-margin-remove" 
                            style={{ width: '50px', height: '50px' }}
                          ></div>
                        )}
                      </td>
                      <td>{blog.title}</td>
                      <td>{blog.author_name}</td>
                      <td>
                        <span className={`uk-label ${blog.status === 'published' ? 'uk-label-success' : 'uk-label-warning'}`}>
                          {blog.status}
                        </span>
                      </td>
                      <td>{new Date(blog.created_at).toLocaleDateString()}</td>
                      <td>
                        <div className="uk-button-group">
                          <Link 
                            to={`/admin/blogs/edit/${blog.id}`} 
                            className="uk-icon-button" 
                            uk-icon="icon: file-edit"
                            uk-tooltip="Edit"
                          ></Link>
                          <Link 
                            to={`/blog/${blog.id}`} 
                            className="uk-icon-button" 
                            uk-icon="icon: eye"
                            uk-tooltip="View"
                          ></Link>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" className="uk-text-center">No blog posts found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="uk-modal uk-open" uk-modal="true" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="uk-modal-dialog">
            <button className="uk-modal-close-default" type="button" uk-close="true" onClick={() => setShowDeleteConfirm(false)}></button>
            <div className="uk-modal-header">
              <h2 className="uk-modal-title">Confirm Deletion</h2>
            </div>
            <div className="uk-modal-body">
              <p>Are you sure you want to delete {selectedBlogs.length} selected blog post(s)? This action cannot be undone.</p>
            </div>
            <div className="uk-modal-footer uk-text-right">
              <button className="uk-button uk-button-default uk-margin-small-right" onClick={() => setShowDeleteConfirm(false)}>Cancel</button>
              <button className="uk-button uk-button-danger" onClick={handleDeleteSelected}>Delete</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlogManagement;
