import React, { useContext, useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';
import productService from '../../services/productService';

const ProductManagement = () => {
  const { user, loading } = useContext(AuthContext);
  const navigate = useNavigate();
  const [products, setProducts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    // Redirect if not admin
    if (!loading && (!user || user.role !== 'admin')) {
      navigate('/login');
    }
    
    // Fetch products
    const fetchProducts = async () => {
      try {
        setIsLoading(true);
        const data = await productService.getAllProducts();
        setProducts(data.products);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError('Failed to fetch products');
        setIsLoading(false);
      }
    };
    
    if (user && user.role === 'admin') {
      fetchProducts();
    }
  }, [user, loading, navigate]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filteredProducts = products.filter(product => 
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectProduct = (id) => {
    if (selectedProducts.includes(id)) {
      setSelectedProducts(selectedProducts.filter(productId => productId !== id));
    } else {
      setSelectedProducts([...selectedProducts, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(product => product.id));
    }
  };

  const handleDeleteSelected = async () => {
    try {
      for (const id of selectedProducts) {
        await productService.deleteProduct(id);
      }
      
      // Refresh products
      const data = await productService.getAllProducts();
      setProducts(data.products);
      setSelectedProducts([]);
      setShowDeleteConfirm(false);
    } catch (err) {
      console.error('Error deleting products:', err);
      setError('Failed to delete products');
    }
  };

  if (loading || isLoading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>Access denied. You must be an admin to view this page.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className="uk-flex uk-flex-between uk-flex-middle">
        <h1 className="uk-heading-divider">Product Management</h1>
        <Link to="/admin/products/new" className="uk-button uk-button-primary">
          <span uk-icon="icon: plus"></span> Add Product
        </Link>
      </div>
      
      <div className="uk-margin">
        <div className="uk-card uk-card-default uk-card-body uk-padding-small">
          <div className="uk-grid-small" uk-grid="true">
            <div className="uk-width-expand">
              <div className="uk-inline uk-width-1-1">
                <span className="uk-form-icon" uk-icon="icon: search"></span>
                <input 
                  className="uk-input" 
                  type="text" 
                  placeholder="Search products..." 
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
            </div>
            <div className="uk-width-auto">
              <button 
                className="uk-button uk-button-danger"
                disabled={selectedProducts.length === 0}
                onClick={() => setShowDeleteConfirm(true)}
              >
                <span uk-icon="icon: trash"></span> Delete
              </button>
            </div>
          </div>
          
          <div className="uk-overflow-auto uk-margin-small-top">
            <table className="uk-table uk-table-divider uk-table-hover uk-table-small uk-table-middle">
              <thead>
                <tr>
                  <th className="uk-table-shrink">
                    <input 
                      className="uk-checkbox" 
                      type="checkbox" 
                      checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th className="uk-table-shrink">Image</th>
                  <th>Name</th>
                  <th>Category</th>
                  <th>Price</th>
                  <th>Stock</th>
                  <th className="uk-table-shrink">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.length > 0 ? (
                  filteredProducts.map(product => (
                    <tr key={product.id}>
                      <td>
                        <input 
                          className="uk-checkbox" 
                          type="checkbox" 
                          checked={selectedProducts.includes(product.id)}
                          onChange={() => handleSelectProduct(product.id)}
                        />
                      </td>
                      <td>
                        {product.image ? (
                          <img 
                            src={`http://localhost:5000${product.image}`} 
                            alt={product.name} 
                            width="50" 
                            height="50"
                            className="uk-border-rounded"
                          />
                        ) : (
                          <div 
                            className="uk-placeholder uk-margin-remove" 
                            style={{ width: '50px', height: '50px' }}
                          ></div>
                        )}
                      </td>
                      <td>{product.name}</td>
                      <td>{product.category}</td>
                      <td>${product.price}</td>
                      <td>{product.stock}</td>
                      <td>
                        <div className="uk-button-group">
                          <Link 
                            to={`/admin/products/edit/${product.id}`} 
                            className="uk-icon-button" 
                            uk-icon="icon: file-edit"
                            uk-tooltip="Edit"
                          ></Link>
                          <Link 
                            to={`/products/${product.id}`} 
                            className="uk-icon-button" 
                            uk-icon="icon: eye"
                            uk-tooltip="View"
                          ></Link>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="7" className="uk-text-center">No products found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="uk-modal uk-open" uk-modal="true" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="uk-modal-dialog">
            <button className="uk-modal-close-default" type="button" uk-close="true" onClick={() => setShowDeleteConfirm(false)}></button>
            <div className="uk-modal-header">
              <h2 className="uk-modal-title">Confirm Deletion</h2>
            </div>
            <div className="uk-modal-body">
              <p>Are you sure you want to delete {selectedProducts.length} selected product(s)? This action cannot be undone.</p>
            </div>
            <div className="uk-modal-footer uk-text-right">
              <button className="uk-button uk-button-default uk-margin-small-right" onClick={() => setShowDeleteConfirm(false)}>Cancel</button>
              <button className="uk-button uk-button-danger" onClick={handleDeleteSelected}>Delete</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductManagement;
