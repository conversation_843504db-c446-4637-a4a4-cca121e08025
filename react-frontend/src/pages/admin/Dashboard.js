import React, { useContext, useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';

const AdminDashboard = () => {
  const { user, loading } = useContext(AuthContext);
  const navigate = useNavigate();
  const [stats, setStats] = useState({
    products: 0,
    blogs: 0,
    users: 0,
    bookings: 0
  });

  useEffect(() => {
    // Redirect if not admin
    if (!loading && (!user || user.role !== 'admin')) {
      navigate('/login');
    }
    
    // Fetch dashboard stats
    const fetchStats = async () => {
      try {
        // In a real app, you would fetch these stats from your API
        // For now, we'll use placeholder data
        setStats({
          products: 3,
          blogs: 2,
          users: 1,
          bookings: 0
        });
      } catch (err) {
        console.error('Error fetching stats:', err);
      }
    };
    
    if (user && user.role === 'admin') {
      fetchStats();
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>Access denied. You must be an admin to view this page.</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="uk-heading-divider">Admin Dashboard</h1>
      
      <div className="uk-grid-match uk-child-width-1-2@s uk-child-width-1-4@m uk-margin" uk-grid="true">
        {/* Products Card */}
        <div>
          <div className="uk-card uk-card-default uk-card-body uk-card-hover">
            <div className="uk-card-badge uk-label uk-background-primary">{stats.products}</div>
            <h3 className="uk-card-title">
              <span uk-icon="icon: tag; ratio: 1.5" className="uk-margin-small-right"></span>
              Products
            </h3>
            <p>Manage your product catalog</p>
            <Link to="/admin/products" className="uk-button uk-button-text">Manage Products</Link>
          </div>
        </div>
        
        {/* Blogs Card */}
        <div>
          <div className="uk-card uk-card-default uk-card-body uk-card-hover">
            <div className="uk-card-badge uk-label uk-background-primary">{stats.blogs}</div>
            <h3 className="uk-card-title">
              <span uk-icon="icon: file-text; ratio: 1.5" className="uk-margin-small-right"></span>
              Blog Posts
            </h3>
            <p>Manage your blog content</p>
            <Link to="/admin/blogs" className="uk-button uk-button-text">Manage Blogs</Link>
          </div>
        </div>
        
        {/* Users Card */}
        <div>
          <div className="uk-card uk-card-default uk-card-body uk-card-hover">
            <div className="uk-card-badge uk-label uk-background-primary">{stats.users}</div>
            <h3 className="uk-card-title">
              <span uk-icon="icon: users; ratio: 1.5" className="uk-margin-small-right"></span>
              Users
            </h3>
            <p>Manage user accounts</p>
            <Link to="/admin/users" className="uk-button uk-button-text">Manage Users</Link>
          </div>
        </div>
        
        {/* Bookings Card */}
        <div>
          <div className="uk-card uk-card-default uk-card-body uk-card-hover">
            <div className="uk-card-badge uk-label uk-background-primary">{stats.bookings}</div>
            <h3 className="uk-card-title">
              <span uk-icon="icon: calendar; ratio: 1.5" className="uk-margin-small-right"></span>
              Bookings
            </h3>
            <p>Manage customer bookings</p>
            <Link to="/admin/bookings" className="uk-button uk-button-text">Manage Bookings</Link>
          </div>
        </div>
      </div>
      
      <div className="uk-margin-medium-top">
        <h2 className="uk-heading-line"><span>Quick Actions</span></h2>
        
        <div className="uk-grid-small uk-child-width-1-2@s uk-child-width-1-4@m" uk-grid="true">
          <div>
            <Link to="/admin/products/new" className="uk-button uk-button-primary uk-width-1-1">
              <span uk-icon="icon: plus"></span> Add Product
            </Link>
          </div>
          <div>
            <Link to="/admin/blogs/new" className="uk-button uk-button-primary uk-width-1-1">
              <span uk-icon="icon: plus"></span> Add Blog Post
            </Link>
          </div>
          <div>
            <Link to="/admin/users" className="uk-button uk-button-default uk-width-1-1">
              <span uk-icon="icon: user"></span> View Users
            </Link>
          </div>
          <div>
            <Link to="/admin/settings" className="uk-button uk-button-default uk-width-1-1">
              <span uk-icon="icon: settings"></span> Settings
            </Link>
          </div>
        </div>
      </div>
      
      <div className="uk-margin-medium-top">
        <h2 className="uk-heading-line"><span>Recent Activity</span></h2>
        
        <div className="uk-card uk-card-default">
          <div className="uk-card-body uk-padding-small">
            <table className="uk-table uk-table-divider uk-table-hover uk-table-small">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>Action</th>
                  <th>User</th>
                  <th>Details</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{new Date().toLocaleDateString()}</td>
                  <td>Product Added</td>
                  <td>Admin</td>
                  <td>Product 3</td>
                </tr>
                <tr>
                  <td>{new Date().toLocaleDateString()}</td>
                  <td>Blog Published</td>
                  <td>Admin</td>
                  <td>Second Blog Post</td>
                </tr>
                <tr>
                  <td>{new Date().toLocaleDateString()}</td>
                  <td>User Registered</td>
                  <td>System</td>
                  <td>New user registration</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
