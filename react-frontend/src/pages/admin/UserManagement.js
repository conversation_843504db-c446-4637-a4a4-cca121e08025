import React, { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../../context/AuthContext';

const UserManagement = () => {
  const { user, loading } = useContext(AuthContext);
  const navigate = useNavigate();
  const [users, setUsers] = useState([
    { id: 1, name: 'Admin User', email: '<EMAIL>', role: 'admin', created_at: new Date() },
    { id: 2, name: 'Test User', email: '<EMAIL>', role: 'user', created_at: new Date() }
  ]);
  // Using isLoading state to track loading state
  const [isLoading] = useState(false);
  // Using error state to display error messages
  const [error] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showRoleModal, setShowRoleModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newRole, setNewRole] = useState('');

  useEffect(() => {
    // Redirect if not admin
    if (!loading && (!user || user.role !== 'admin')) {
      navigate('/login');
    }

    // In a real app, you would fetch users from your API
    // For now, we'll use the mock data defined above
  }, [user, loading, navigate]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectUser = (id) => {
    if (selectedUsers.includes(id)) {
      setSelectedUsers(selectedUsers.filter(userId => userId !== id));
    } else {
      setSelectedUsers([...selectedUsers, id]);
    }
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map(user => user.id));
    }
  };

  const handleDeleteSelected = () => {
    // In a real app, you would call your API to delete users
    setUsers(users.filter(user => !selectedUsers.includes(user.id)));
    setSelectedUsers([]);
    setShowDeleteConfirm(false);
  };

  const openRoleModal = (user) => {
    setSelectedUser(user);
    setNewRole(user.role);
    setShowRoleModal(true);
  };

  const handleRoleChange = (e) => {
    setNewRole(e.target.value);
  };

  const handleUpdateRole = () => {
    // In a real app, you would call your API to update the user's role
    setUsers(users.map(u =>
      u.id === selectedUser.id ? { ...u, role: newRole } : u
    ));
    setShowRoleModal(false);
  };

  if (loading || isLoading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>Access denied. You must be an admin to view this page.</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="uk-heading-divider">User Management</h1>

      <div className="uk-margin">
        <div className="uk-card uk-card-default uk-card-body uk-padding-small">
          <div className="uk-grid-small" uk-grid="true">
            <div className="uk-width-expand">
              <div className="uk-inline uk-width-1-1">
                <span className="uk-form-icon" uk-icon="icon: search"></span>
                <input
                  className="uk-input"
                  type="text"
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
            </div>
            <div className="uk-width-auto">
              <button
                className="uk-button uk-button-danger"
                disabled={selectedUsers.length === 0}
                onClick={() => setShowDeleteConfirm(true)}
              >
                <span uk-icon="icon: trash"></span> Delete
              </button>
            </div>
          </div>

          <div className="uk-overflow-auto uk-margin-small-top">
            <table className="uk-table uk-table-divider uk-table-hover uk-table-small uk-table-middle">
              <thead>
                <tr>
                  <th className="uk-table-shrink">
                    <input
                      className="uk-checkbox"
                      type="checkbox"
                      checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                      onChange={handleSelectAll}
                    />
                  </th>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Role</th>
                  <th>Joined</th>
                  <th className="uk-table-shrink">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredUsers.length > 0 ? (
                  filteredUsers.map(user => (
                    <tr key={user.id}>
                      <td>
                        <input
                          className="uk-checkbox"
                          type="checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={() => handleSelectUser(user.id)}
                        />
                      </td>
                      <td>{user.name}</td>
                      <td>{user.email}</td>
                      <td>
                        <span className={`uk-label ${user.role === 'admin' ? 'uk-label-danger' : 'uk-label-primary'}`}>
                          {user.role}
                        </span>
                      </td>
                      <td>{new Date(user.created_at).toLocaleDateString()}</td>
                      <td>
                        <div className="uk-button-group">
                          <button
                            className="uk-icon-button"
                            uk-icon="icon: cog"
                            uk-tooltip="Change Role"
                            onClick={() => openRoleModal(user)}
                          ></button>
                          <button
                            className="uk-icon-button"
                            uk-icon="icon: ban"
                            uk-tooltip="Ban User"
                          ></button>
                        </div>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="uk-text-center">No users found</td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="uk-modal uk-open" uk-modal="true" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="uk-modal-dialog">
            <button className="uk-modal-close-default" type="button" uk-close="true" onClick={() => setShowDeleteConfirm(false)}></button>
            <div className="uk-modal-header">
              <h2 className="uk-modal-title">Confirm Deletion</h2>
            </div>
            <div className="uk-modal-body">
              <p>Are you sure you want to delete {selectedUsers.length} selected user(s)? This action cannot be undone.</p>
            </div>
            <div className="uk-modal-footer uk-text-right">
              <button className="uk-button uk-button-default uk-margin-small-right" onClick={() => setShowDeleteConfirm(false)}>Cancel</button>
              <button className="uk-button uk-button-danger" onClick={handleDeleteSelected}>Delete</button>
            </div>
          </div>
        </div>
      )}

      {/* Change Role Modal */}
      {showRoleModal && selectedUser && (
        <div className="uk-modal uk-open" uk-modal="true" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.6)' }}>
          <div className="uk-modal-dialog">
            <button className="uk-modal-close-default" type="button" uk-close="true" onClick={() => setShowRoleModal(false)}></button>
            <div className="uk-modal-header">
              <h2 className="uk-modal-title">Change User Role</h2>
            </div>
            <div className="uk-modal-body">
              <p>Change role for user: <strong>{selectedUser.name}</strong></p>

              <div className="uk-margin">
                <label className="uk-form-label" htmlFor="role">Role</label>
                <div className="uk-form-controls">
                  <select
                    className="uk-select"
                    id="role"
                    value={newRole}
                    onChange={handleRoleChange}
                  >
                    <option value="user">User</option>
                    <option value="admin">Admin</option>
                  </select>
                </div>
              </div>
            </div>
            <div className="uk-modal-footer uk-text-right">
              <button className="uk-button uk-button-default uk-margin-small-right" onClick={() => setShowRoleModal(false)}>Cancel</button>
              <button className="uk-button uk-button-primary" onClick={handleUpdateRole}>Update Role</button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserManagement;
