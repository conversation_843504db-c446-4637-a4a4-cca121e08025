import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import blogService from '../services/blogService';
import { AuthContext } from '../context/AuthContext';
import { getImageUrl } from '../utils/imageUtils';

const Blog = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchBlogs();
  }, []);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const data = await blogService.getAllBlogs();
      setBlogs(data.blogs);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching blogs:', err);
      setError('Failed to fetch blog posts');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <button className="uk-alert-close" uk-close="true"></button>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className="uk-flex uk-flex-between uk-flex-middle uk-margin-medium-bottom">
        <h1 className="uk-heading-divider">Blog</h1>
        {user && (
          <Link to="/admin/blogs/new" className="uk-button uk-button-primary">
            <span uk-icon="icon: plus"></span> Create New Post
          </Link>
        )}
      </div>

      {blogs.length > 0 ? (
        <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-2@s" uk-grid="true">
          {blogs.map(blog => (
            <div key={blog.id}>
              <div className="uk-card uk-card-default uk-card-hover blog-card">
                <div className="uk-card-media-top">
                  {blog.image && (
                    <img
                      src={getImageUrl(blog.image)}
                      alt={blog.title}
                    />
                  )}
                </div>
                <div className="uk-card-body">
                  <h3 className="uk-card-title">{blog.title}</h3>
                  <p className="uk-text-meta">
                    By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}
                  </p>
                  <p>
                    {blog.content.length > 150
                      ? `${blog.content.substring(0, 150)}...`
                      : blog.content}
                  </p>
                </div>
                <div className="uk-card-footer">
                  <Link to={`/blog/${blog.id}`} className="uk-button uk-button-text">Read More</Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="uk-alert-primary" uk-alert="true">
          <p>No blog posts found</p>
        </div>
      )}
    </div>
  );
};

export default Blog;
