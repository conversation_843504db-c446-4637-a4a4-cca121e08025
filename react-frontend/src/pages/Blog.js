import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import blogService from '../services/blogService';
import { AuthContext } from '../context/AuthContext';

const Blog = () => {
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    fetchBlogs();
  }, []);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      const data = await blogService.getAllBlogs();
      setBlogs(data.blogs);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching blogs:', err);
      setError('Failed to fetch blog posts');
      setLoading(false);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div>
      <div className="blog-header">
        <h1>Blog</h1>
        {user && (
          <Link to="/blog/new" className="btn">Create New Post</Link>
        )}
      </div>
      
      {blogs.length > 0 ? (
        <div className="blog-list">
          {blogs.map(blog => (
            <div key={blog.id} className="card blog-card">
              {blog.image && (
                <img 
                  src={`http://localhost:5000${blog.image}`} 
                  alt={blog.title} 
                  className="blog-image"
                />
              )}
              <div className="blog-content">
                <h2>{blog.title}</h2>
                <p className="blog-meta">
                  By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}
                </p>
                <p className="blog-excerpt">
                  {blog.content.length > 200 
                    ? `${blog.content.substring(0, 200)}...` 
                    : blog.content}
                </p>
                <Link to={`/blog/${blog.id}`} className="btn">Read More</Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="alert alert-info">No blog posts found</div>
      )}
    </div>
  );
};

export default Blog;
