import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import blogService from '../services/blogService';
import { AuthContext } from '../context/AuthContext';
import { getImageUrl } from '../utils/imageUtils';

const BlogDetail = () => {
  const { id } = useParams();
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setLoading(true);
        const data = await blogService.getBlogById(id);
        setBlog(data.blog);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching blog:', err);
        setError('Failed to fetch blog post');
        setLoading(false);
      }
    };

    fetchBlog();
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this blog post?')) {
      try {
        await blogService.deleteBlog(id);
        navigate('/blog');
      } catch (err) {
        console.error('Error deleting blog:', err);
        setError('Failed to delete blog post');
      }
    }
  };

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <button className="uk-alert-close" uk-close="true"></button>
        <p>{error}</p>
      </div>
    );
  }

  if (!blog) {
    return (
      <div className="uk-alert-primary" uk-alert="true">
        <p>Blog post not found</p>
      </div>
    );
  }

  // Check if user is the author or admin
  const isAuthor = user && (user.id === blog.author_id || user.role === 'admin');

  return (
    <div>
      <h1 className="uk-heading-divider">{blog.title}</h1>

      <div className="uk-card uk-card-default uk-card-body">
        <p className="uk-text-meta uk-margin-medium-bottom">
          By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}
        </p>

        {blog.image && (
          <div className="blog-detail-image-container">
            <img
              src={getImageUrl(blog.image)}
              alt={blog.title}
              className="blog-detail-image"
            />
          </div>
        )}

        <div className="uk-margin-medium-bottom">
          {blog.content.split('\n').map((paragraph, index) => (
            <p key={index}>{paragraph}</p>
          ))}
        </div>

        {isAuthor && (
          <div className="uk-margin-medium-top uk-flex uk-flex-right">
            <Link to={`/admin/blogs/edit/${blog.id}`} className="uk-button uk-button-primary uk-margin-small-right">Edit</Link>
            <button onClick={handleDelete} className="uk-button uk-button-danger">Delete</button>
          </div>
        )}
      </div>

      <div className="uk-margin-medium-top">
        <Link to="/blog" className="uk-button uk-button-default">
          <span uk-icon="icon: arrow-left"></span> Back to Blog
        </Link>
      </div>
    </div>
  );
};

export default BlogDetail;
