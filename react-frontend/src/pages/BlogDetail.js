import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link, useNavigate } from 'react-router-dom';
import blogService from '../services/blogService';
import { AuthContext } from '../context/AuthContext';
import { getImageUrl } from '../utils/imageUtils';

const BlogDetail = () => {
  const { id } = useParams();
  const [blog, setBlog] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchBlog = async () => {
      try {
        setLoading(true);
        const data = await blogService.getBlogById(id);
        setBlog(data.blog);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching blog:', err);
        setError('Failed to fetch blog post');
        setLoading(false);
      }
    };

    fetchBlog();
  }, [id]);

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this blog post?')) {
      try {
        await blogService.deleteBlog(id);
        navigate('/blog');
      } catch (err) {
        console.error('Error deleting blog:', err);
        setError('Failed to delete blog post');
      }
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!blog) {
    return <div className="alert alert-info">Blog post not found</div>;
  }

  // Check if user is the author or admin
  const isAuthor = user && (user.id === blog.author_id || user.role === 'admin');

  return (
    <div className="blog-detail">
      <div className="card">
        <h1>{blog.title}</h1>
        <p className="blog-meta">
          By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}
        </p>

        {blog.image && (
          <img
            src={getImageUrl(blog.image)}
            alt={blog.title}
            className="blog-detail-image"
          />
        )}

        <div className="blog-content">
          {blog.content.split('\n').map((paragraph, index) => (
            <p key={index}>{paragraph}</p>
          ))}
        </div>

        {isAuthor && (
          <div className="blog-actions">
            <Link to={`/blog/edit/${blog.id}`} className="btn">Edit</Link>
            <button onClick={handleDelete} className="btn btn-danger">Delete</button>
          </div>
        )}
      </div>

      <div className="blog-navigation">
        <Link to="/blog" className="btn">Back to Blog</Link>
      </div>
    </div>
  );
};

export default BlogDetail;
