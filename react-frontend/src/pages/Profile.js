import React, { useState, useContext, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const Profile = () => {
  const { user, loading, error, updateProfile, changePassword, logout } = useContext(AuthContext);
  const navigate = useNavigate();

  const [profileData, setProfileData] = useState({
    name: '',
    email: ''
  });

  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [activeTab, setActiveTab] = useState('profile');
  const [profileSuccess, setProfileSuccess] = useState('');
  const [profileError, setProfileError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');
  const [passwordError, setPasswordError] = useState('');

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !user) {
      navigate('/login');
    }

    // Set profile data from user
    if (user) {
      setProfileData({
        name: user.name || '',
        email: user.email || ''
      });
    }
  }, [user, loading, navigate]);

  const handleProfileChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  const handlePasswordChange = (e) => {
    const { name, value } = e.target;
    setPasswordData(prevData => ({
      ...prevData,
      [name]: value
    }));
  };

  const handleProfileSubmit = async (e) => {
    e.preventDefault();
    setProfileSuccess('');
    setProfileError('');

    try {
      await updateProfile(profileData);
      setProfileSuccess('Profile updated successfully');
    } catch (err) {
      setProfileError(err.response?.data?.message || 'Failed to update profile');
    }
  };

  const handlePasswordSubmit = async (e) => {
    e.preventDefault();
    setPasswordSuccess('');
    setPasswordError('');

    // Validate passwords
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setPasswordError('New password must be at least 6 characters long');
      return;
    }

    try {
      await changePassword(passwordData.currentPassword, passwordData.newPassword);
      setPasswordSuccess('Password changed successfully');
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    } catch (err) {
      setPasswordError(err.response?.data?.message || 'Failed to change password');
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <a className="uk-alert-close" uk-close="true"></a>
        <p>{error}</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="uk-alert-primary" uk-alert="true">
        <p>Please log in to view your profile</p>
      </div>
    );
  }

  return (
    <div className="uk-section">
      <div className="uk-container uk-container-small">
        <h1 className="uk-heading-medium uk-text-center">My Profile</h1>

        <div className="uk-margin-medium-top">
          <ul className="uk-tab uk-flex-center" uk-tab="connect: #profile-tabs; animation: uk-animation-fade">
            <li className={activeTab === 'profile' ? 'uk-active' : ''}>
              <a href="#" onClick={() => setActiveTab('profile')}>Profile</a>
            </li>
            <li className={activeTab === 'password' ? 'uk-active' : ''}>
              <a href="#" onClick={() => setActiveTab('password')}>Change Password</a>
            </li>
          </ul>

          <ul id="profile-tabs" className="uk-switcher uk-margin">
            <li>
              {/* Profile Tab */}
              <div className="uk-card uk-card-default uk-card-body uk-box-shadow-small">
                {profileSuccess && (
                  <div className="uk-alert-success" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{profileSuccess}</p>
                  </div>
                )}

                {profileError && (
                  <div className="uk-alert-danger" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{profileError}</p>
                  </div>
                )}

                <form onSubmit={handleProfileSubmit} className="uk-form-stacked">
                  <div className="uk-margin">
                    <label className="uk-form-label" htmlFor="name">Name</label>
                    <div className="uk-form-controls">
                      <input
                        className="uk-input"
                        type="text"
                        id="name"
                        name="name"
                        value={profileData.name}
                        onChange={handleProfileChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <label className="uk-form-label" htmlFor="email">Email</label>
                    <div className="uk-form-controls">
                      <input
                        className="uk-input"
                        type="email"
                        id="email"
                        name="email"
                        value={profileData.email}
                        onChange={handleProfileChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <button type="submit" className="uk-button uk-button-primary">
                      Update Profile
                    </button>
                  </div>
                </form>
              </div>
            </li>

            <li>
              {/* Password Tab */}
              <div className="uk-card uk-card-default uk-card-body uk-box-shadow-small">
                {passwordSuccess && (
                  <div className="uk-alert-success" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{passwordSuccess}</p>
                  </div>
                )}

                {passwordError && (
                  <div className="uk-alert-danger" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{passwordError}</p>
                  </div>
                )}

                <form onSubmit={handlePasswordSubmit} className="uk-form-stacked">
                  <div className="uk-margin">
                    <label className="uk-form-label" htmlFor="currentPassword">Current Password</label>
                    <div className="uk-form-controls">
                      <input
                        className="uk-input"
                        type="password"
                        id="currentPassword"
                        name="currentPassword"
                        value={passwordData.currentPassword}
                        onChange={handlePasswordChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <label className="uk-form-label" htmlFor="newPassword">New Password</label>
                    <div className="uk-form-controls">
                      <input
                        className="uk-input"
                        type="password"
                        id="newPassword"
                        name="newPassword"
                        value={passwordData.newPassword}
                        onChange={handlePasswordChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <label className="uk-form-label" htmlFor="confirmPassword">Confirm New Password</label>
                    <div className="uk-form-controls">
                      <input
                        className="uk-input"
                        type="password"
                        id="confirmPassword"
                        name="confirmPassword"
                        value={passwordData.confirmPassword}
                        onChange={handlePasswordChange}
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <button type="submit" className="uk-button uk-button-primary">
                      Change Password
                    </button>
                  </div>
                </form>
              </div>
            </li>
          </ul>
        </div>

        <div className="uk-margin-medium-top uk-text-center">
          <button onClick={handleLogout} className="uk-button uk-button-danger">
            <span uk-icon="icon: sign-out"></span> Logout
          </button>
        </div>
      </div>
    </div>
  );
};

export default Profile;
