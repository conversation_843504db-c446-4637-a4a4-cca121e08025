import React, { useState, useEffect, useContext } from 'react';
import { useParams, Link } from 'react-router-dom';
import productService from '../services/productService';
import { AuthContext } from '../context/AuthContext';

const ProductDetail = () => {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { user } = useContext(AuthContext);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true);
        const data = await productService.getProductById(id);
        setProduct(data.product);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching product:', err);
        setError('Failed to fetch product details');
        setLoading(false);
      }
    };

    fetchProduct();
  }, [id]);

  const handleQuantityChange = (e) => {
    const value = parseInt(e.target.value);
    if (value > 0 && value <= (product?.stock || 1)) {
      setQuantity(value);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!product) {
    return <div className="alert alert-info">Product not found</div>;
  }

  return (
    <div className="product-detail">
      <div className="card">
        <div className="product-detail-grid">
          <div className="product-image">
            {product.image ? (
              <img 
                src={`http://localhost:5000${product.image}`} 
                alt={product.name} 
              />
            ) : (
              <div className="no-image">No image available</div>
            )}
          </div>
          
          <div className="product-info">
            <h1>{product.name}</h1>
            <p className="product-price">${product.price}</p>
            <p className="product-category">Category: {product.category}</p>
            <p className="product-stock">
              {product.stock > 0 ? (
                <span className="in-stock">In Stock ({product.stock} available)</span>
              ) : (
                <span className="out-of-stock">Out of Stock</span>
              )}
            </p>
            
            <div className="product-description">
              <h3>Description</h3>
              <p>{product.description}</p>
            </div>
            
            {product.stock > 0 && (
              <div className="product-actions">
                <div className="form-group">
                  <label htmlFor="quantity">Quantity:</label>
                  <input
                    type="number"
                    id="quantity"
                    min="1"
                    max={product.stock}
                    value={quantity}
                    onChange={handleQuantityChange}
                  />
                </div>
                
                {user ? (
                  <button className="btn">Add to Cart</button>
                ) : (
                  <Link to="/login" className="btn">Login to Purchase</Link>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
      
      <div className="product-navigation">
        <Link to="/products" className="btn">Back to Products</Link>
      </div>
    </div>
  );
};

export default ProductDetail;
