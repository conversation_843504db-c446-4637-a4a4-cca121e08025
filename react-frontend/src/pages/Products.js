import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import productService from '../services/productService';
import { getImageUrl } from '../utils/imageUtils';

const Products = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    category: '',
    minPrice: '',
    maxPrice: '',
    search: ''
  });

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await productService.getAllProducts(filters);
      setProducts(data.products);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching products:', err);
      setError('Failed to fetch products');
      setLoading(false);
    }
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prevFilters => ({
      ...prevFilters,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    fetchProducts();
  };

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <a className="uk-alert-close" uk-close="true"></a>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <h1 className="uk-heading-divider">Products</h1>

      <div className="uk-card uk-card-default uk-card-body uk-margin">
        <h3 className="uk-card-title">Filter Products</h3>

        <form onSubmit={handleSubmit} className="uk-grid-small" uk-grid="true">
          <div className="uk-width-1-1">
            <div className="uk-inline uk-width-1-1">
              <span className="uk-form-icon" uk-icon="icon: search"></span>
              <input
                className="uk-input"
                type="text"
                id="search"
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Search products..."
              />
            </div>
          </div>

          <div className="uk-width-1-3@s">
            <label className="uk-form-label" htmlFor="category">Category</label>
            <div className="uk-form-controls">
              <select
                className="uk-select"
                id="category"
                name="category"
                value={filters.category}
                onChange={handleFilterChange}
              >
                <option value="">All Categories</option>
                <option value="Category 1">Category 1</option>
                <option value="Category 2">Category 2</option>
              </select>
            </div>
          </div>

          <div className="uk-width-1-3@s">
            <label className="uk-form-label" htmlFor="minPrice">Min Price</label>
            <div className="uk-form-controls">
              <input
                className="uk-input"
                type="number"
                id="minPrice"
                name="minPrice"
                value={filters.minPrice}
                onChange={handleFilterChange}
                placeholder="Min Price"
              />
            </div>
          </div>

          <div className="uk-width-1-3@s">
            <label className="uk-form-label" htmlFor="maxPrice">Max Price</label>
            <div className="uk-form-controls">
              <input
                className="uk-input"
                type="number"
                id="maxPrice"
                name="maxPrice"
                value={filters.maxPrice}
                onChange={handleFilterChange}
                placeholder="Max Price"
              />
            </div>
          </div>

          <div className="uk-width-1-1 uk-text-right">
            <button type="submit" className="uk-button uk-button-primary">
              <span uk-icon="icon: refresh"></span> Apply Filters
            </button>
          </div>
        </form>
      </div>

      {products.length > 0 ? (
        <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-2@s uk-child-width-1-1" uk-grid="true">
          {products.map(product => (
            <div key={product.id}>
              <div className="uk-card uk-card-default uk-card-hover">
                <div className="uk-card-media-top">
                  {product.image && (
                    <img
                      src={getImageUrl(product.image)}
                      alt={product.name}
                      className="uk-width-1-1"
                    />
                  )}
                </div>
                <div className="uk-card-body">
                  <h3 className="uk-card-title">{product.name}</h3>
                  <p className="uk-text-bold uk-text-primary">${product.price}</p>
                  <p className="uk-text-truncate">{product.description}</p>
                </div>
                <div className="uk-card-footer">
                  <Link to={`/products/${product.id}`} className="uk-button uk-button-text">View Details</Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="uk-alert-primary" uk-alert="true">
          <p>No products found. Try adjusting your filters.</p>
        </div>
      )}
    </div>
  );
};

export default Products;
