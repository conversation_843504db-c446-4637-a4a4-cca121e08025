import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import productService from '../services/productService';
import blogService from '../services/blogService';
import { getImageUrl } from '../utils/imageUtils';
import SkeletonLoader from '../components/SkeletonLoader';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [latestBlogs, setLatestBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch featured products (limit to 3)
        const productsData = await productService.getAllProducts({ limit: 3 });
        setFeaturedProducts(productsData.products);

        // Fetch latest blog posts (limit to 3)
        const blogsData = await blogService.getAllBlogs({ limit: 3 });
        setLatestBlogs(blogsData.blogs);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <button className="uk-alert-close" uk-close="true"></button>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      {/* Hero Section with Parallax */}
      <div className="uk-section uk-section-primary uk-light uk-padding-large uk-background-cover uk-background-center-center uk-background-fixed"
           style={{
             backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
             minHeight: '70vh'
           }}
           uk-parallax="bgy: -200">
        <div className="uk-container uk-position-relative">
          <div className="uk-grid-match uk-flex uk-flex-middle" uk-grid="true" style={{ minHeight: '50vh' }}>
            <div className="uk-width-1-1 uk-text-center">
              <h1 className="uk-heading-large" uk-parallax="y: -100,0">
                Welcome to React Backend App
              </h1>
              <p className="uk-text-lead uk-margin-medium-top" uk-parallax="y: -50,0; opacity: 0,1">
                A full-stack application with Node.js/Express backend and React frontend.
              </p>
              <div className="uk-margin-large-top" uk-parallax="y: 50,0; opacity: 0,1">
                <Link to="/products" className="uk-button uk-button-default uk-button-large">
                  Browse Products
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Products Section */}
      <div className="uk-section" style={{
        background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
        position: 'relative'
      }}>
        <div className="uk-container">
          <h2 className="uk-heading-line uk-text-center" uk-parallax="y: -50,0">
            <span>Featured Products</span>
          </h2>

          {loading ? (
            <SkeletonLoader type="card" count={3} />
          ) : (
            <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-2@s" uk-grid="true">
              {featuredProducts.map(product => (
                <div key={product.id} uk-parallax="y: 100,0; opacity: 0,1">
                  <div className="glass-card uk-card-body uk-text-center">
                    <div className="uk-card-media-top">
                      {product.image && (
                        <img
                          src={getImageUrl(product.image)}
                          alt={product.name}
                          className="uk-width-1-1"
                          style={{ borderRadius: '12px' }}
                        />
                      )}
                    </div>
                    <div className="uk-margin-medium-top">
                      <h3 className="uk-card-title uk-text-bold">{product.name}</h3>
                      <p className="uk-text-large uk-text-primary uk-margin-small">${product.price}</p>
                      <p className="uk-text-small uk-margin-medium">{product.description}</p>
                      <Link to={`/products/${product.id}`} className="uk-button uk-button-primary uk-button-small">
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="uk-margin-medium-top uk-text-center">
            <Link to="/products" className="uk-button uk-button-primary">View All Products</Link>
          </div>
        </div>
      </div>

      {/* Latest Blog Posts Section */}
      <div className="uk-section uk-section-muted">
        <div className="uk-container">
          <h2 className="uk-heading-line uk-text-center"><span>Latest Blog Posts</span></h2>

          {loading ? (
            <SkeletonLoader type="card" count={3} />
          ) : (
            <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-1@s" uk-grid="true">
              {latestBlogs.map(blog => (
                <div key={blog.id}>
                  <div className="uk-card uk-card-default uk-card-hover">
                    {blog.image && (
                      <div className="uk-card-media-top">
                        <img
                          src={getImageUrl(blog.image)}
                          alt={blog.title}
                          className="uk-width-1-1"
                        />
                      </div>
                    )}
                    <div className="uk-card-body">
                      <h3 className="uk-card-title">{blog.title}</h3>
                      <p>{blog.content.substring(0, 100)}...</p>
                      <p className="uk-text-meta">
                        By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="uk-card-footer">
                      <Link to={`/blog/${blog.id}`} className="uk-button uk-button-text">Read More</Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="uk-margin-medium-top uk-text-center">
            <Link to="/blog" className="uk-button uk-button-primary">View All Posts</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
