import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import productService from '../services/productService';
import blogService from '../services/blogService';
import { getImageUrl } from '../utils/imageUtils';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [latestBlogs, setLatestBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch featured products (limit to 3)
        const productsData = await productService.getAllProducts({ limit: 3 });
        setFeaturedProducts(productsData.products);

        // Fetch latest blog posts (limit to 3)
        const blogsData = await blogService.getAllBlogs({ limit: 3 });
        setLatestBlogs(blogsData.blogs);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="uk-flex uk-flex-center uk-margin-large-top">
        <div uk-spinner="ratio: 3"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="uk-alert-danger" uk-alert="true">
        <button className="uk-alert-close" uk-close="true"></button>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      {/* Hero Section */}
      <div className="uk-section uk-section-primary uk-light uk-padding">
        <div className="uk-container">
          <div className="uk-grid-match" uk-grid="true">
            <div className="uk-width-1-1 uk-text-center">
              <h1 className="uk-heading-medium">Welcome to React Backend App</h1>
              <p className="uk-text-lead">A full-stack application with Node.js/Express backend and React frontend.</p>
              <Link to="/products" className="uk-button uk-button-default uk-button-large">Browse Products</Link>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Products Section */}
      <div className="uk-section">
        <div className="uk-container">
          <h2 className="uk-heading-line uk-text-center"><span>Featured Products</span></h2>

          <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-2@s" uk-grid="true">
            {featuredProducts.map(product => (
              <div key={product.id}>
                <div className="uk-card uk-card-default uk-card-hover">
                  <div className="uk-card-media-top">
                    {product.image && (
                      <img
                        src={getImageUrl(product.image)}
                        alt={product.name}
                        className="uk-width-1-1"
                      />
                    )}
                  </div>
                  <div className="uk-card-body">
                    <h3 className="uk-card-title">{product.name}</h3>
                    <p className="uk-text-bold uk-text-primary">${product.price}</p>
                    <p className="uk-text-truncate">{product.description}</p>
                  </div>
                  <div className="uk-card-footer">
                    <Link to={`/products/${product.id}`} className="uk-button uk-button-text">View Details</Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="uk-margin-medium-top uk-text-center">
            <Link to="/products" className="uk-button uk-button-primary">View All Products</Link>
          </div>
        </div>
      </div>

      {/* Latest Blog Posts Section */}
      <div className="uk-section uk-section-muted">
        <div className="uk-container">
          <h2 className="uk-heading-line uk-text-center"><span>Latest Blog Posts</span></h2>

          <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-1@s" uk-grid="true">
            {latestBlogs.map(blog => (
              <div key={blog.id}>
                <div className="uk-card uk-card-default uk-card-hover">
                  {blog.image && (
                    <div className="uk-card-media-top">
                      <img
                        src={getImageUrl(blog.image)}
                        alt={blog.title}
                        className="uk-width-1-1"
                      />
                    </div>
                  )}
                  <div className="uk-card-body">
                    <h3 className="uk-card-title">{blog.title}</h3>
                    <p>{blog.content.substring(0, 100)}...</p>
                    <p className="uk-text-meta">
                      By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="uk-card-footer">
                    <Link to={`/blog/${blog.id}`} className="uk-button uk-button-text">Read More</Link>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="uk-margin-medium-top uk-text-center">
            <Link to="/blog" className="uk-button uk-button-primary">View All Posts</Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;
