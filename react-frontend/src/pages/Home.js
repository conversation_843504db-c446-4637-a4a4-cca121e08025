import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import productService from '../services/productService';
import blogService from '../services/blogService';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [latestBlogs, setLatestBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch featured products (limit to 3)
        const productsData = await productService.getAllProducts({ limit: 3 });
        setFeaturedProducts(productsData.products);
        
        // Fetch latest blog posts (limit to 3)
        const blogsData = await blogService.getAllBlogs({ limit: 3 });
        setLatestBlogs(blogsData.blogs);
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  return (
    <div>
      <section className="hero">
        <div className="card">
          <h1>Welcome to React Backend App</h1>
          <p>A full-stack application with Node.js/Express backend and React frontend.</p>
          <Link to="/products" className="btn">Browse Products</Link>
        </div>
      </section>

      <section className="featured-products">
        <h2>Featured Products</h2>
        <div className="product-grid">
          {featuredProducts.map(product => (
            <div key={product.id} className="product-card">
              {product.image && (
                <img 
                  src={`http://localhost:5000${product.image}`} 
                  alt={product.name} 
                />
              )}
              <div className="product-card-body">
                <h3 className="product-card-title">{product.name}</h3>
                <p className="product-card-price">${product.price}</p>
                <Link to={`/products/${product.id}`} className="btn">View Details</Link>
              </div>
            </div>
          ))}
        </div>
        <div className="text-center">
          <Link to="/products" className="btn">View All Products</Link>
        </div>
      </section>

      <section className="latest-blogs">
        <h2>Latest Blog Posts</h2>
        <div className="blog-grid">
          {latestBlogs.map(blog => (
            <div key={blog.id} className="card">
              <h3>{blog.title}</h3>
              <p>{blog.content.substring(0, 150)}...</p>
              <p>By {blog.author_name} on {new Date(blog.created_at).toLocaleDateString()}</p>
              <Link to={`/blog/${blog.id}`} className="btn">Read More</Link>
            </div>
          ))}
        </div>
        <div className="text-center">
          <Link to="/blog" className="btn">View All Posts</Link>
        </div>
      </section>
    </div>
  );
};

export default Home;
