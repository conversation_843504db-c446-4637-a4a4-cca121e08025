import React, { useState, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');

  const { login, loading, error } = useContext(AuthContext);
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Form validation
    if (!email || !password) {
      setFormError('Please fill in all fields');
      return;
    }

    try {
      await login(email, password);
      navigate('/profile'); // Redirect to profile page after successful login
    } catch (err) {
      console.error('Login error:', err);
      // Error is handled by the AuthContext
    }
  };

  return (
    <div className="uk-section uk-section-muted uk-flex uk-flex-middle uk-animation-fade" uk-height-viewport="true">
      <div className="uk-width-1-1">
        <div className="uk-container">
          <div className="uk-grid-margin uk-grid uk-grid-stack" uk-grid="true">
            <div className="uk-width-1-1@m">
              <div className="uk-margin uk-width-large uk-margin-auto uk-card uk-card-default uk-card-body uk-box-shadow-large">
                <h3 className="uk-card-title uk-text-center">Login</h3>

                {error && (
                  <div className="uk-alert-danger" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{error}</p>
                  </div>
                )}

                {formError && (
                  <div className="uk-alert-danger" uk-alert="true">
                    <a className="uk-alert-close" uk-close="true"></a>
                    <p>{formError}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit}>
                  <div className="uk-margin">
                    <div className="uk-inline uk-width-1-1">
                      <span className="uk-form-icon" uk-icon="icon: mail"></span>
                      <input
                        className="uk-input uk-form-large"
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Email"
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <div className="uk-inline uk-width-1-1">
                      <span className="uk-form-icon" uk-icon="icon: lock"></span>
                      <input
                        className="uk-input uk-form-large"
                        type="password"
                        id="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Password"
                        required
                      />
                    </div>
                  </div>

                  <div className="uk-margin">
                    <button
                      className="uk-button uk-button-primary uk-button-large uk-width-1-1"
                      type="submit"
                      disabled={loading}
                    >
                      {loading ? (
                        <span>
                          <div uk-spinner="ratio: 0.8"></div> Logging in...
                        </span>
                      ) : 'Login'}
                    </button>
                  </div>
                </form>

                <div className="uk-text-small uk-text-center">
                  <Link to="/forgot-password" className="uk-button uk-button-link">Forgot Password?</Link>
                </div>

                <hr className="uk-divider-icon" />

                <div className="uk-text-center">
                  <p className="uk-text-small">Don't have an account?</p>
                  <Link to="/register" className="uk-button uk-button-default uk-width-1-1">Register</Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
