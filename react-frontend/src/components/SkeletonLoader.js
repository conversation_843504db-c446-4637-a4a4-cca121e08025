import React from 'react';

const SkeletonLoader = ({ type = 'card', count = 3 }) => {
  const renderCardSkeleton = () => (
    <div className="uk-card uk-card-default skeleton-card">
      <div className="skeleton skeleton-image"></div>
      <div className="uk-card-body">
        <div className="skeleton skeleton-title"></div>
        <div className="skeleton skeleton-text"></div>
        <div className="skeleton skeleton-text short"></div>
        <div className="skeleton skeleton-button"></div>
      </div>
    </div>
  );

  const renderListSkeleton = () => (
    <div className="uk-card uk-card-default uk-card-body skeleton-card">
      <div className="skeleton skeleton-title"></div>
      <div className="skeleton skeleton-text"></div>
      <div className="skeleton skeleton-text"></div>
      <div className="skeleton skeleton-text short"></div>
    </div>
  );

  const renderTableSkeleton = () => (
    <tr>
      <td><div className="skeleton skeleton-text"></div></td>
      <td><div className="skeleton skeleton-text short"></div></td>
      <td><div className="skeleton skeleton-text short"></div></td>
      <td><div className="skeleton skeleton-button"></div></td>
    </tr>
  );

  const renderSkeletons = () => {
    const skeletons = [];
    for (let i = 0; i < count; i++) {
      switch (type) {
        case 'card':
          skeletons.push(
            <div key={i} className="uk-width-1-3@m uk-width-1-2@s">
              {renderCardSkeleton()}
            </div>
          );
          break;
        case 'list':
          skeletons.push(
            <div key={i} className="uk-margin-medium-bottom">
              {renderListSkeleton()}
            </div>
          );
          break;
        case 'table':
          skeletons.push(renderTableSkeleton());
          break;
        default:
          skeletons.push(
            <div key={i} className="uk-width-1-3@m uk-width-1-2@s">
              {renderCardSkeleton()}
            </div>
          );
      }
    }
    return skeletons;
  };

  if (type === 'table') {
    return <>{renderSkeletons()}</>;
  }

  if (type === 'list') {
    return <div>{renderSkeletons()}</div>;
  }

  return (
    <div className="uk-grid-match uk-child-width-1-3@m uk-child-width-1-2@s" uk-grid="true">
      {renderSkeletons()}
    </div>
  );
};

export default SkeletonLoader;
