/**
 * Utility functions for handling image URLs
 */

// Get the base URL for the backend API (without the /api suffix)
const getBaseUrl = () => {
  // Use the environment variable or extract from API_URL
  const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
  
  // Remove the /api suffix if present
  return apiUrl.replace(/\/api$/, '');
};

/**
 * Get the full URL for an image path
 * @param {string} imagePath - The image path from the API (e.g., /images/products/image.jpg)
 * @returns {string} - The full URL for the image
 */
export const getImageUrl = (imagePath) => {
  if (!imagePath) return '';
  
  // If the image path already starts with http, return it as is
  if (imagePath.startsWith('http')) {
    return imagePath;
  }
  
  // Make sure the image path starts with a slash
  const normalizedPath = imagePath.startsWith('/') ? imagePath : `/${imagePath}`;
  
  // Combine the base URL with the image path
  return `${getBaseUrl()}${normalizedPath}`;
};

export default {
  getImageUrl
};
