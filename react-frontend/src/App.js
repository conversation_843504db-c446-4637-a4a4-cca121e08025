import React, { useContext } from 'react';
import { BrowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom';
import Home from './pages/Home';
import Products from './pages/Products';
import ProductDetail from './pages/ProductDetail';
import Blog from './pages/Blog';
import BlogDetail from './pages/BlogDetail';
import Login from './pages/Login';
import Register from './pages/Register';
import Profile from './pages/Profile';
import { AuthProvider, AuthContext } from './context/AuthContext';

// Navigation component with UIkit styling
const Navigation = () => {
  const { user, logout } = useContext(AuthContext);
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path ? 'uk-active' : '';
  };

  return (
    <header>
      <nav className="uk-navbar-container" uk-navbar="true">
        <div className="uk-navbar-left">
          <ul className="uk-navbar-nav">
            <li className="uk-navbar-item uk-logo">
              <Link to="/">React Backend</Link>
            </li>
          </ul>
        </div>

        <div className="uk-navbar-center">
          <ul className="uk-navbar-nav uk-visible@m">
            <li className={isActive('/')}>
              <Link to="/">Home</Link>
            </li>
            <li className={isActive('/products')}>
              <Link to="/products">Products</Link>
            </li>
            <li className={isActive('/blog')}>
              <Link to="/blog">Blog</Link>
            </li>
          </ul>
        </div>

        <div className="uk-navbar-right">
          <ul className="uk-navbar-nav">
            {user ? (
              <li>
                <a href="#">
                  <span uk-icon="user"></span>
                  <span className="uk-visible@m">{user.name}</span>
                </a>
                <div className="uk-navbar-dropdown">
                  <ul className="uk-nav uk-navbar-dropdown-nav">
                    <li>
                      <Link to="/profile">My Profile</Link>
                    </li>
                    <li className="uk-nav-divider"></li>
                    <li>
                      <a href="#" onClick={(e) => { e.preventDefault(); logout(); }}>
                        Logout
                      </a>
                    </li>
                  </ul>
                </div>
              </li>
            ) : (
              <>
                <li className={isActive('/login')}>
                  <Link to="/login">Login</Link>
                </li>
                <li className={isActive('/register')}>
                  <Link to="/register">Register</Link>
                </li>
              </>
            )}
          </ul>

          <a href="#" className="uk-navbar-toggle uk-hidden@m" uk-navbar-toggle-icon="true" uk-toggle="target: #offcanvas-nav"></a>
        </div>
      </nav>

      {/* Mobile Navigation */}
      <div id="offcanvas-nav" uk-offcanvas="overlay: true">
        <div className="uk-offcanvas-bar">
          <button className="uk-offcanvas-close" type="button" uk-close="true"></button>
          <h3>Menu</h3>
          <ul className="uk-nav uk-nav-default">
            <li className={isActive('/')}>
              <Link to="/">Home</Link>
            </li>
            <li className={isActive('/products')}>
              <Link to="/products">Products</Link>
            </li>
            <li className={isActive('/blog')}>
              <Link to="/blog">Blog</Link>
            </li>
            <li className="uk-nav-divider"></li>
            {user ? (
              <>
                <li className={isActive('/profile')}>
                  <Link to="/profile">My Profile</Link>
                </li>
                <li>
                  <a href="#" onClick={(e) => { e.preventDefault(); logout(); }}>
                    Logout
                  </a>
                </li>
              </>
            ) : (
              <>
                <li className={isActive('/login')}>
                  <Link to="/login">Login</Link>
                </li>
                <li className={isActive('/register')}>
                  <Link to="/register">Register</Link>
                </li>
              </>
            )}
          </ul>
        </div>
      </div>
    </header>
  );
};

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="uk-offcanvas-content">
          <Routes>
            <Route path="*" element={
              <>
                <Navigation />
                <div className="uk-container uk-margin-medium-top uk-margin-medium-bottom">
                  <Routes>
                    <Route path="/" element={<Home />} />
                    <Route path="/products" element={<Products />} />
                    <Route path="/products/:id" element={<ProductDetail />} />
                    <Route path="/blog" element={<Blog />} />
                    <Route path="/blog/:id" element={<BlogDetail />} />
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />
                    <Route path="/profile" element={<Profile />} />
                  </Routes>
                </div>
                <footer className="uk-section uk-section-secondary uk-padding-small">
                  <div className="uk-container">
                    <div className="uk-grid uk-text-center uk-text-left@s">
                      <div className="uk-width-1-1">
                        <p>&copy; {new Date().getFullYear()} React Backend. All rights reserved.</p>
                      </div>
                    </div>
                  </div>
                </footer>
              </>
            } />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
