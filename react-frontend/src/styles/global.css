/* Global styles for the application */

/* Navigation styling improvements */
/* Logo styling - moved 2px right and darker */
.uk-navbar-item.uk-logo {
  padding-left: 2px;
  font-weight: 700;
  color: #111 !important;
  letter-spacing: 0.5px;
}

/* Navigation links - darker and bolder */
.uk-navbar-nav > li > a {
  font-size: 1rem;
  font-weight: 600;
  color: #222 !important;
}

.uk-navbar-nav > li.uk-active > a {
  font-weight: 700;
  color: #1e87f0 !important;
}

.uk-navbar-nav > li > a:hover {
  color: #1e87f0 !important;
}

/* Dropdown menu - darker text */
.uk-navbar-dropdown-nav > li > a {
  font-weight: 500;
  color: #222 !important;
}

.uk-navbar-dropdown-nav > li > a:hover {
  color: #1e87f0 !important;
}

/* Mobile navigation - darker text */
.uk-offcanvas-bar .uk-nav-default > li > a {
  color: #fff !important;
  font-weight: 500;
}

.uk-offcanvas-bar .uk-nav-default > li.uk-active > a {
  color: #fff !important;
  font-weight: 700;
}

/* User name in navigation */
.uk-navbar-nav > li > a > span {
  font-weight: 600;
  color: #222 !important;
}

/* Make headings slightly more prominent */
h1 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #222;
  margin-bottom: 1.2rem;
}

h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #222;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #222;
  margin-bottom: 0.8rem;
}

/* Improve card appearance */
.uk-card {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease-in-out;
}

.uk-card:hover {
  box-shadow: 0 14px 25px rgba(0, 0, 0, 0.16);
}

.uk-card-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #222;
}

/* Admin dashboard styles */
.admin-dashboard .uk-card {
  border-radius: 4px;
}

.admin-dashboard .uk-card-title {
  font-size: 1.3rem;
  font-weight: 600;
}

/* Form improvements */
.uk-form-label {
  font-weight: 500;
  margin-bottom: 5px;
}

.uk-input:focus,
.uk-select:focus,
.uk-textarea:focus {
  border-color: #1e87f0;
  box-shadow: 0 0 0 0.2rem rgba(30, 135, 240, 0.25);
}

/* Button improvements */
.uk-button {
  font-weight: 500;
  text-transform: none;
  border-radius: 3px;
}

.uk-button-primary {
  background-color: #1e87f0;
}

.uk-button-primary:hover {
  background-color: #0f7ae5;
}

.uk-button-secondary {
  background-color: #222;
}

.uk-button-danger {
  background-color: #f0506e;
}

.uk-button-danger:hover {
  background-color: #ee395b;
}

/* Table improvements */
.uk-table th {
  font-weight: 600;
  color: #333;
}

/* Alert improvements */
.uk-alert {
  border-radius: 4px;
}

/* Pagination improvements */
.uk-pagination > li > a {
  border-radius: 3px;
}

/* Image wrapper for consistent sizing */
.image-wrapper {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.image-wrapper img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Blog card image wrapper */
.blog-card .uk-card-media-top {
  height: 220px;
  overflow: hidden;
}

.blog-card .uk-card-media-top img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Product card image wrapper */
.uk-card-default .uk-card-media-top {
  height: 220px;
  overflow: hidden;
}

.uk-card-default .uk-card-media-top img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Blog detail image */
.blog-detail-image-container {
  width: 100%;
  max-height: 400px;
  overflow: hidden;
  margin-bottom: 20px;
}

.blog-detail-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Loading Skeletons */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.skeleton-image {
  width: 100%;
  height: 220px;
  border-radius: 4px;
  margin-bottom: 15px;
}

.skeleton-title {
  height: 24px;
  width: 80%;
  border-radius: 4px;
  margin-bottom: 10px;
}

.skeleton-text {
  height: 16px;
  width: 100%;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-button {
  height: 36px;
  width: 120px;
  border-radius: 4px;
  margin-top: 15px;
}

/* Glassmorphism Effects */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.35);
  transform: translateY(-5px);
  box-shadow: 0 15px 35px 0 rgba(31, 38, 135, 0.4);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  border-radius: 16px;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
  color: white;
}

.glass-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Neumorphism Effects */
.neuro-card {
  background: #e0e5ec;
  border-radius: 20px;
  box-shadow:
    9px 9px 16px #a3b1c6,
    -9px -9px 16px #ffffff;
  transition: all 0.3s ease;
}

.neuro-card:hover {
  box-shadow:
    inset 9px 9px 16px #a3b1c6,
    inset -9px -9px 16px #ffffff;
}

.neuro-button {
  background: #e0e5ec;
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  box-shadow:
    6px 6px 12px #a3b1c6,
    -6px -6px 12px #ffffff;
  transition: all 0.2s ease;
  cursor: pointer;
}

.neuro-button:hover {
  box-shadow:
    inset 6px 6px 12px #a3b1c6,
    inset -6px -6px 12px #ffffff;
}

.neuro-button:active {
  box-shadow:
    inset 8px 8px 16px #a3b1c6,
    inset -8px -8px 16px #ffffff;
}
