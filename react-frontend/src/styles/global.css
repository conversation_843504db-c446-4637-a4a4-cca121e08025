/* Global styles for the application */

/* Subtle improvements to navigation */
.uk-navbar-nav > li > a {
  font-size: 1rem;
  font-weight: 500;
}

.uk-navbar-nav > li.uk-active > a {
  font-weight: 600;
}

/* Make headings slightly more prominent */
h1 {
  font-size: 2.2rem;
  font-weight: 700;
  color: #222;
  margin-bottom: 1.2rem;
}

h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #222;
  margin-bottom: 1rem;
}

h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #222;
  margin-bottom: 0.8rem;
}

/* Improve card appearance */
.uk-card {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: box-shadow 0.3s ease-in-out;
}

.uk-card:hover {
  box-shadow: 0 14px 25px rgba(0, 0, 0, 0.16);
}

.uk-card-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #222;
}

/* Admin dashboard styles */
.admin-dashboard .uk-card {
  border-radius: 4px;
}

.admin-dashboard .uk-card-title {
  font-size: 1.3rem;
  font-weight: 600;
}

/* Form improvements */
.uk-form-label {
  font-weight: 500;
  margin-bottom: 5px;
}

.uk-input:focus,
.uk-select:focus,
.uk-textarea:focus {
  border-color: #1e87f0;
  box-shadow: 0 0 0 0.2rem rgba(30, 135, 240, 0.25);
}

/* Button improvements */
.uk-button {
  font-weight: 500;
  text-transform: none;
  border-radius: 3px;
}

.uk-button-primary {
  background-color: #1e87f0;
}

.uk-button-primary:hover {
  background-color: #0f7ae5;
}

.uk-button-secondary {
  background-color: #222;
}

.uk-button-danger {
  background-color: #f0506e;
}

.uk-button-danger:hover {
  background-color: #ee395b;
}

/* Table improvements */
.uk-table th {
  font-weight: 600;
  color: #333;
}

/* Alert improvements */
.uk-alert {
  border-radius: 4px;
}

/* Pagination improvements */
.uk-pagination > li > a {
  border-radius: 3px;
}
