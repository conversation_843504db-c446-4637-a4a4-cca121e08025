/* Global styles for the application */

/* Make navigation menu text bigger and darker */
.uk-navbar-nav > li > a {
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  color: #222 !important;
}

.uk-navbar-nav > li.uk-active > a {
  color: #1e87f0 !important;
  font-weight: 700 !important;
}

.uk-navbar-nav > li > a:hover {
  color: #1e87f0 !important;
}

.uk-navbar-item, .uk-navbar-toggle {
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  color: #222 !important;
}

/* Make dropdown menu text bigger and darker */
.uk-navbar-dropdown-nav > li > a {
  font-size: 1rem !important;
  font-weight: 500 !important;
  color: #222 !important;
}

.uk-navbar-dropdown-nav > li > a:hover {
  color: #1e87f0 !important;
}

/* Make mobile navigation text bigger and darker */
.uk-nav > li > a {
  font-size: 1.1rem !important;
  font-weight: 600 !important;
  color: #222 !important;
}

.uk-nav-default > li.uk-active > a {
  color: #1e87f0 !important;
  font-weight: 700 !important;
}

/* Make headings bigger and darker */
h1 {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #222 !important;
  margin-bottom: 1.5rem !important;
}

h2 {
  font-size: 2rem !important;
  font-weight: 600 !important;
  color: #222 !important;
  margin-bottom: 1.2rem !important;
}

h3 {
  font-size: 1.75rem !important;
  font-weight: 600 !important;
  color: #222 !important;
  margin-bottom: 1rem !important;
}

/* Make card titles bigger and darker */
.uk-card-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: #222 !important;
}

/* Make section headings bigger and darker */
.uk-heading-divider,
.uk-heading-line,
.uk-heading-bullet {
  font-size: 2.2rem !important;
  font-weight: 700 !important;
  color: #222 !important;
}

/* Make hero text bigger and bolder */
.uk-heading-medium {
  font-size: 2.8rem !important;
  font-weight: 800 !important;
  color: #222 !important;
}

.uk-heading-large {
  font-size: 3.5rem !important;
  font-weight: 800 !important;
  color: #222 !important;
}

/* Make article titles bigger and darker */
.uk-article-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: #222 !important;
}

/* Add a subtle text shadow to headings for better readability */
h1, h2, h3, .uk-heading-medium, .uk-heading-large, .uk-heading-divider, .uk-article-title {
  text-shadow: 0 1px 1px rgba(255, 255, 255, 0.8) !important;
}

/* Make page section titles stand out */
.page-title {
  font-size: 2.8rem !important;
  font-weight: 800 !important;
  color: #222 !important;
  margin-bottom: 2rem !important;
  position: relative !important;
}

.page-title:after {
  content: '' !important;
  position: absolute !important;
  bottom: -10px !important;
  left: 0 !important;
  width: 80px !important;
  height: 4px !important;
  background-color: #1e87f0 !important;
}

/* Make admin dashboard headings more prominent */
.admin-dashboard h1 {
  font-size: 2.8rem !important;
  color: #1e3a8a !important;
}

.admin-dashboard .uk-card-title {
  font-size: 1.6rem !important;
  font-weight: 700 !important;
}
