import api from './api';

const productService = {
  // Get all products
  getAllProducts: async (filters = {}) => {
    return api.get('/products', { params: filters });
  },

  // Get product by ID
  getProductById: async (id) => {
    return api.get(`/products/${id}`);
  },

  // Create a new product (admin only)
  createProduct: async (productData) => {
    const formData = new FormData();
    
    // Append product data to form data
    Object.keys(productData).forEach(key => {
      if (key === 'image' && productData[key] instanceof File) {
        formData.append(key, productData[key]);
      } else {
        formData.append(key, productData[key]);
      }
    });
    
    return api.post('/products', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Update product (admin only)
  updateProduct: async (id, productData) => {
    const formData = new FormData();
    
    // Append product data to form data
    Object.keys(productData).forEach(key => {
      if (key === 'image' && productData[key] instanceof File) {
        formData.append(key, productData[key]);
      } else {
        formData.append(key, productData[key]);
      }
    });
    
    return api.put(`/products/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Delete product (admin only)
  deleteProduct: async (id) => {
    return api.delete(`/products/${id}`);
  }
};

export default productService;
