import api from './api';

const authService = {
  // Register a new user
  register: async (name, email, password) => {
    return api.post('/auth/register', { name, email, password });
  },

  // Login user
  login: async (email, password) => {
    return api.post('/auth/login', { email, password });
  },

  // Get user profile
  getProfile: async () => {
    return api.get('/auth/profile');
  },

  // Update user profile
  updateProfile: async (userData) => {
    return api.put('/auth/profile', userData);
  },

  // Change password
  changePassword: async (currentPassword, newPassword) => {
    return api.post('/auth/change-password', { currentPassword, newPassword });
  },

  // Request password reset
  requestPasswordReset: async (email) => {
    return api.post('/auth/request-reset', { email });
  },

  // Reset password
  resetPassword: async (token, password) => {
    return api.post('/auth/reset-password', { token, password });
  }
};

export default authService;
