import api from './api';

const blogService = {
  // Get all blog posts
  getAllBlogs: async (filters = {}) => {
    return api.get('/blogs', { params: filters });
  },

  // Get blog post by ID
  getBlogById: async (id) => {
    return api.get(`/blogs/${id}`);
  },

  // Create a new blog post
  createBlog: async (blogData) => {
    const formData = new FormData();
    
    // Append blog data to form data
    Object.keys(blogData).forEach(key => {
      if (key === 'image' && blogData[key] instanceof File) {
        formData.append(key, blogData[key]);
      } else {
        formData.append(key, blogData[key]);
      }
    });
    
    return api.post('/blogs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Update blog post
  updateBlog: async (id, blogData) => {
    const formData = new FormData();
    
    // Append blog data to form data
    Object.keys(blogData).forEach(key => {
      if (key === 'image' && blogData[key] instanceof File) {
        formData.append(key, blogData[key]);
      } else {
        formData.append(key, blogData[key]);
      }
    });
    
    return api.put(`/blogs/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  // Delete blog post
  deleteBlog: async (id) => {
    return api.delete(`/blogs/${id}`);
  }
};

export default blogService;
