const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Get database configuration from environment variables
const {
  DB_HOST,
  DB_USER,
  DB_PASSWORD,
  DB_NAME
} = process.env;

// Read SQL file
const sqlFilePath = path.join(__dirname, 'database.sql');
const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

// Split SQL script into individual statements
const statements = sqlScript
  .replace(/(\r\n|\n|\r)/gm, ' ') // Remove newlines
  .replace(/\s+/g, ' ') // Replace multiple spaces with a single space
  .split(';') // Split on semicolons
  .map(statement => statement.trim())
  .filter(statement => statement.length > 0); // Remove empty statements

async function setupDatabase() {
  let connection;
  
  try {
    // First connect without database to create it if it doesn't exist
    connection = await mysql.createConnection({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD
    });
    
    console.log('Connected to MySQL server');
    
    // Create database if it doesn't exist
    await connection.query(`CREATE DATABASE IF NOT EXISTS ${DB_NAME}`);
    console.log(`Database ${DB_NAME} created or already exists`);
    
    // Close connection
    await connection.end();
    
    // Connect with database selected
    connection = await mysql.createConnection({
      host: DB_HOST,
      user: DB_USER,
      password: DB_PASSWORD,
      database: DB_NAME,
      multipleStatements: true
    });
    
    console.log(`Connected to database ${DB_NAME}`);
    
    // Execute each SQL statement
    for (const statement of statements) {
      if (statement.length > 0) {
        await connection.query(statement);
        console.log('Executed SQL statement');
      }
    }
    
    console.log('Database setup completed successfully');
  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

setupDatabase();
