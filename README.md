# React Backend Project

A full-stack application with Node.js/Express backend and React frontend.

## Features

- User authentication (register, login, password reset)
- Product management
- Blog system
- Booking functionality
- Payment integration
- File uploads
- Email notifications

## Tech Stack

### Backend
- Node.js
- Express.js
- MySQL
- JWT Authentication
- Multer for file uploads
- <PERSON>demail<PERSON> for emails
- <PERSON> for logging

### Frontend (to be implemented)
- React.js
- React Router
- State management (Redux or Context API)
- Axios for API calls
- Responsive design

## Project Structure

```
backend/
├── config/             # Configuration files
├── controllers/        # Request handlers
├── middleware/         # Custom middleware
├── models/             # Database models
├── routes/             # API routes
├── services/           # Business logic
├── utils/              # Utility functions
├── views/              # Email templates
└── public/             # Static files
```

## Getting Started

### Prerequisites

- Node.js (v14 or higher)
- MySQL

### Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd react-backend-project
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Set up environment variables:
   - Create a `.env` file in the root directory
   - Copy the contents from `.env.example` and update with your values

4. Set up the database:
   - Create a MySQL database
   - Import the schema from `database.sql`
   ```
   mysql -u username -p database_name < database.sql
   ```

5. Start the development server:
   ```
   npm run dev
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile
- `PUT /api/auth/profile` - Update user profile
- `POST /api/auth/change-password` - Change password
- `POST /api/auth/request-reset` - Request password reset
- `POST /api/auth/reset-password` - Reset password

### Products
- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create a new product (admin)
- `PUT /api/products/:id` - Update product (admin)
- `DELETE /api/products/:id` - Delete product (admin)

### Blogs
- `GET /api/blogs` - Get all blog posts
- `GET /api/blogs/:id` - Get blog post by ID
- `POST /api/blogs` - Create a new blog post
- `PUT /api/blogs/:id` - Update blog post
- `DELETE /api/blogs/:id` - Delete blog post

### Bookings
- `GET /api/bookings` - Get all bookings
- `GET /api/bookings/:id` - Get booking by ID
- `POST /api/bookings` - Create a new booking
- `PATCH /api/bookings/:id/status` - Update booking status
- `DELETE /api/bookings/:id` - Delete booking (admin)

### Payments
- `POST /api/payments/create-intent` - Create payment intent
- `POST /api/payments/process` - Process payment
- `GET /api/payments` - Get all payments
- `GET /api/payments/:id` - Get payment by ID
- `POST /api/payments/webhook` - Payment webhook

## Deployment

### Local Development
1. Start the server:
   ```
   npm run dev
   ```

### Production Deployment
1. Build the application:
   ```
   npm run build
   ```

2. Start the production server:
   ```
   npm start
   ```

## Environment Variables

```
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=react_backend_db

# JWT Configuration
JWT_SECRET=your-secret-key
JWT_EXPIRATION=24h

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# Payment Gateway Configuration
PAYMENT_API_KEY=your-payment-api-key
PAYMENT_SECRET_KEY=your-payment-secret-key
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.
