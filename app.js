const express = require('express');
const cors = require('cors');
const path = require('path');
const { FRONTEND_URL } = require('./backend/config/env.config');
const logger = require('./backend/utils/logger');

// Import routes
const authRoutes = require('./backend/routes/auth.routes');
const productRoutes = require('./backend/routes/product.routes');
const blogRoutes = require('./backend/routes/blog.routes');
const bookingRoutes = require('./backend/routes/booking.routes');
const paymentRoutes = require('./backend/routes/payment.routes');

// Import middleware
const { notFound, errorHandler, dbErrorHandler } = require('./backend/middleware/error.middleware');

// Initialize express app
const app = express();

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// CORS configuration
app.use(cors({
  origin: function(origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);

    const allowedOrigins = FRONTEND_URL.split(',');
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Static files
app.use('/images', express.static(path.join(__dirname, 'backend/public/images')));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/products', productRoutes);
app.use('/api/blogs', blogRoutes);
app.use('/api/bookings', bookingRoutes);
app.use('/api/payments', paymentRoutes);

// API health check
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'API is running'
  });
});

// Error handling middleware
app.use(dbErrorHandler);
app.use(notFound);
app.use(errorHandler);

module.exports = app;
