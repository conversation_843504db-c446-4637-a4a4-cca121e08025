const fs = require('fs').promises;
const path = require('path');
const logger = require('./logger');

/**
 * Load email template from file and replace placeholders with values
 * @param {string} templateName - Name of the template file (without extension)
 * @param {Object} replacements - Object with key-value pairs for replacements
 * @returns {Promise<string>} - HTML content of the email
 */
const loadEmailTemplate = async (templateName, replacements = {}) => {
  try {
    // Get the template path
    const templatePath = path.join(__dirname, '../views/emails', `${templateName}.html`);
    
    // Read the template file
    let template = await fs.readFile(templatePath, 'utf8');
    
    // Replace placeholders with values
    Object.keys(replacements).forEach(key => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      template = template.replace(regex, replacements[key]);
    });
    
    return template;
  } catch (error) {
    logger.error(`Error loading email template: ${error.message}`);
    throw error;
  }
};

/**
 * Format email address with name
 * @param {string} email - Email address
 * @param {string} name - Name
 * @returns {string} - Formatted email address
 */
const formatEmailAddress = (email, name) => {
  if (name) {
    return `"${name}" <${email}>`;
  }
  return email;
};

/**
 * Validate email address format
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if valid, false otherwise
 */
const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

module.exports = {
  loadEmailTemplate,
  formatEmailAddress,
  isValidEmail
};
