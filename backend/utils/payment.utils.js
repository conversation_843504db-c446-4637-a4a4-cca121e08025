const crypto = require('crypto');
const logger = require('./logger');
const { PAYMENT_API_KEY, PAYMENT_SECRET_KEY } = require('../config/env.config');

/**
 * Generate a unique transaction ID
 * @returns {string} - Unique transaction ID
 */
const generateTransactionId = () => {
  const timestamp = Date.now().toString();
  const random = Math.random().toString().substring(2, 8);
  return `TXN_${timestamp}_${random}`;
};

/**
 * Calculate payment signature for verification
 * @param {Object} data - Payment data
 * @param {string} secretKey - Secret key for signature
 * @returns {string} - Calculated signature
 */
const calculatePaymentSignature = (data, secretKey = PAYMENT_SECRET_KEY) => {
  // Sort the keys to ensure consistent order
  const sortedKeys = Object.keys(data).sort();
  
  // Create a string with key=value pairs
  const signatureString = sortedKeys
    .map(key => `${key}=${data[key]}`)
    .join('&');
  
  // Create HMAC SHA256 hash
  const hmac = crypto.createHmac('sha256', secretKey);
  hmac.update(signatureString);
  
  return hmac.digest('hex');
};

/**
 * Verify payment signature
 * @param {Object} data - Payment data
 * @param {string} signature - Signature to verify
 * @param {string} secretKey - Secret key for verification
 * @returns {boolean} - True if signature is valid
 */
const verifyPaymentSignature = (data, signature, secretKey = PAYMENT_SECRET_KEY) => {
  const calculatedSignature = calculatePaymentSignature(data, secretKey);
  return calculatedSignature === signature;
};

/**
 * Format currency amount
 * @param {number} amount - Amount to format
 * @param {string} currency - Currency code (default: USD)
 * @returns {string} - Formatted amount
 */
const formatCurrency = (amount, currency = 'USD') => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency
  }).format(amount);
};

module.exports = {
  generateTransactionId,
  calculatePaymentSignature,
  verifyPaymentSignature,
  formatCurrency
};
