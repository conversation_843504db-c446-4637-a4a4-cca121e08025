<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Booking Confirmation</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #4a90e2;
      color: white;
      padding: 20px;
      text-align: center;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .booking-details {
      background-color: white;
      border: 1px solid #ddd;
      padding: 15px;
      margin: 20px 0;
      border-radius: 4px;
    }
    .booking-details table {
      width: 100%;
      border-collapse: collapse;
    }
    .booking-details th, .booking-details td {
      padding: 10px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    .button {
      display: inline-block;
      background-color: #4a90e2;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin: 20px 0;
    }
    .footer {
      text-align: center;
      padding: 20px;
      font-size: 12px;
      color: #777;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Booking Confirmation</h1>
    </div>
    <div class="content">
      <p>Hello {{name}},</p>
      <p>Thank you for your booking. Your booking has been confirmed!</p>
      
      <div class="booking-details">
        <h2>Booking Details</h2>
        <table>
          <tr>
            <th>Booking ID:</th>
            <td>{{bookingId}}</td>
          </tr>
          <tr>
            <th>Product:</th>
            <td>{{productName}}</td>
          </tr>
          <tr>
            <th>Quantity:</th>
            <td>{{quantity}}</td>
          </tr>
          <tr>
            <th>Total Price:</th>
            <td>${{totalPrice}}</td>
          </tr>
          <tr>
            <th>Booking Date:</th>
            <td>{{bookingDate}}</td>
          </tr>
        </table>
      </div>
      
      <p style="text-align: center;">
        <a href="{{frontendUrl}}/bookings/{{bookingId}}" class="button">View Booking</a>
      </p>
      
      <p>If you have any questions or need to make changes to your booking, please contact our support team.</p>
      <p>Thank you,<br>The React Backend Team</p>
    </div>
    <div class="footer">
      <p>This is an automated email. Please do not reply to this message.</p>
    </div>
  </div>
</body>
</html>
