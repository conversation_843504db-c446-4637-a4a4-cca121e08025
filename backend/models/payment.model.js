const db = require('../config/db.config');

class Payment {
  constructor(payment) {
    this.id = payment.id;
    this.booking_id = payment.booking_id;
    this.user_id = payment.user_id;
    this.amount = payment.amount;
    this.currency = payment.currency || 'USD';
    this.payment_method = payment.payment_method;
    this.transaction_id = payment.transaction_id;
    this.status = payment.status || 'pending';
    this.created_at = payment.created_at;
    this.updated_at = payment.updated_at;
    
    // Additional fields from joins
    this.user_name = payment.user_name;
    this.user_email = payment.user_email;
  }

  // Create a new payment
  static async create(newPayment) {
    try {
      const sql = `
        INSERT INTO payments (booking_id, user_id, amount, currency, payment_method, transaction_id, status)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      const result = await db.query(sql, [
        newPayment.booking_id,
        newPayment.user_id,
        newPayment.amount,
        newPayment.currency || 'USD',
        newPayment.payment_method,
        newPayment.transaction_id,
        newPayment.status || 'pending'
      ]);

      return { id: result.insertId, ...newPayment };
    } catch (error) {
      throw error;
    }
  }

  // Find payment by ID
  static async findById(id) {
    try {
      const sql = `
        SELECT p.*, u.name as user_name, u.email as user_email
        FROM payments p
        JOIN users u ON p.user_id = u.id
        WHERE p.id = ?
      `;
      const rows = await db.query(sql, [id]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return new Payment(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Find payment by transaction ID
  static async findByTransactionId(transactionId) {
    try {
      const sql = `
        SELECT p.*, u.name as user_name, u.email as user_email
        FROM payments p
        JOIN users u ON p.user_id = u.id
        WHERE p.transaction_id = ?
      `;
      const rows = await db.query(sql, [transactionId]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return new Payment(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Update payment
  static async update(id, paymentData) {
    try {
      // Build the SQL query dynamically based on provided fields
      const fields = Object.keys(paymentData)
        .filter(key => key !== 'id' && !key.includes('user_name') && !key.includes('user_email')) // Exclude id and joined fields
        .map(key => `${key} = ?`);
      
      const values = Object.keys(paymentData)
        .filter(key => key !== 'id' && !key.includes('user_name') && !key.includes('user_email'))
        .map(key => paymentData[key]);
      
      values.push(id); // Add id for WHERE clause
      
      const sql = `
        UPDATE payments 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await db.query(sql, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get all payments
  static async findAll(filters = {}) {
    try {
      let sql = `
        SELECT p.*, u.name as user_name, u.email as user_email
        FROM payments p
        JOIN users u ON p.user_id = u.id
      `;
      const values = [];
      
      // Add WHERE clauses for filters
      const whereClauses = [];
      
      if (filters.user_id) {
        whereClauses.push('p.user_id = ?');
        values.push(filters.user_id);
      }
      
      if (filters.booking_id) {
        whereClauses.push('p.booking_id = ?');
        values.push(filters.booking_id);
      }
      
      if (filters.status) {
        whereClauses.push('p.status = ?');
        values.push(filters.status);
      }
      
      if (filters.payment_method) {
        whereClauses.push('p.payment_method = ?');
        values.push(filters.payment_method);
      }
      
      if (whereClauses.length > 0) {
        sql += ' WHERE ' + whereClauses.join(' AND ');
      }
      
      // Add sorting
      if (filters.sortBy) {
        const sortDirection = filters.sortDir === 'desc' ? 'DESC' : 'ASC';
        sql += ` ORDER BY p.${filters.sortBy} ${sortDirection}`;
      } else {
        sql += ' ORDER BY p.created_at DESC';
      }
      
      // Add pagination
      if (filters.limit) {
        sql += ' LIMIT ?';
        values.push(parseInt(filters.limit));
        
        if (filters.offset) {
          sql += ' OFFSET ?';
          values.push(parseInt(filters.offset));
        }
      }
      
      const rows = await db.query(sql, values);
      return rows.map(row => new Payment(row));
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Payment;
