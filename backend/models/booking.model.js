const db = require('../config/db.config');

class Booking {
  constructor(booking) {
    this.id = booking.id;
    this.user_id = booking.user_id;
    this.product_id = booking.product_id;
    this.quantity = booking.quantity;
    this.total_price = booking.total_price;
    this.status = booking.status || 'pending';
    this.payment_id = booking.payment_id;
    this.booking_date = booking.booking_date;
    this.created_at = booking.created_at;
    this.updated_at = booking.updated_at;
    
    // Additional fields from joins
    this.product_name = booking.product_name;
    this.user_name = booking.user_name;
    this.user_email = booking.user_email;
  }

  // Create a new booking
  static async create(newBooking) {
    try {
      const sql = `
        INSERT INTO bookings (user_id, product_id, quantity, total_price, status, payment_id, booking_date)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      const result = await db.query(sql, [
        newBooking.user_id,
        newBooking.product_id,
        newBooking.quantity,
        newBooking.total_price,
        newBooking.status || 'pending',
        newBooking.payment_id,
        newBooking.booking_date || new Date()
      ]);

      return { id: result.insertId, ...newBooking };
    } catch (error) {
      throw error;
    }
  }

  // Find booking by ID
  static async findById(id) {
    try {
      const sql = `
        SELECT b.*, p.name as product_name, u.name as user_name, u.email as user_email
        FROM bookings b
        JOIN products p ON b.product_id = p.id
        JOIN users u ON b.user_id = u.id
        WHERE b.id = ?
      `;
      const rows = await db.query(sql, [id]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return new Booking(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Update booking
  static async update(id, bookingData) {
    try {
      // Build the SQL query dynamically based on provided fields
      const fields = Object.keys(bookingData)
        .filter(key => key !== 'id' && !key.includes('_name') && !key.includes('user_email')) // Exclude id and joined fields
        .map(key => `${key} = ?`);
      
      const values = Object.keys(bookingData)
        .filter(key => key !== 'id' && !key.includes('_name') && !key.includes('user_email'))
        .map(key => bookingData[key]);
      
      values.push(id); // Add id for WHERE clause
      
      const sql = `
        UPDATE bookings 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await db.query(sql, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete booking
  static async delete(id) {
    try {
      const sql = 'DELETE FROM bookings WHERE id = ?';
      const result = await db.query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get all bookings
  static async findAll(filters = {}) {
    try {
      let sql = `
        SELECT b.*, p.name as product_name, u.name as user_name, u.email as user_email
        FROM bookings b
        JOIN products p ON b.product_id = p.id
        JOIN users u ON b.user_id = u.id
      `;
      const values = [];
      
      // Add WHERE clauses for filters
      const whereClauses = [];
      
      if (filters.user_id) {
        whereClauses.push('b.user_id = ?');
        values.push(filters.user_id);
      }
      
      if (filters.status) {
        whereClauses.push('b.status = ?');
        values.push(filters.status);
      }
      
      if (filters.product_id) {
        whereClauses.push('b.product_id = ?');
        values.push(filters.product_id);
      }
      
      if (filters.date_from) {
        whereClauses.push('b.booking_date >= ?');
        values.push(filters.date_from);
      }
      
      if (filters.date_to) {
        whereClauses.push('b.booking_date <= ?');
        values.push(filters.date_to);
      }
      
      if (whereClauses.length > 0) {
        sql += ' WHERE ' + whereClauses.join(' AND ');
      }
      
      // Add sorting
      if (filters.sortBy) {
        const sortDirection = filters.sortDir === 'desc' ? 'DESC' : 'ASC';
        sql += ` ORDER BY b.${filters.sortBy} ${sortDirection}`;
      } else {
        sql += ' ORDER BY b.created_at DESC';
      }
      
      // Add pagination
      if (filters.limit) {
        sql += ' LIMIT ?';
        values.push(parseInt(filters.limit));
        
        if (filters.offset) {
          sql += ' OFFSET ?';
          values.push(parseInt(filters.offset));
        }
      }
      
      const rows = await db.query(sql, values);
      return rows.map(row => new Booking(row));
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Booking;
