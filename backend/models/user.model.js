const db = require('../config/db.config');
const bcrypt = require('bcrypt');

class User {
  constructor(user) {
    this.id = user.id;
    this.name = user.name;
    this.email = user.email;
    this.password = user.password;
    this.role = user.role || 'user';
    this.created_at = user.created_at;
    this.updated_at = user.updated_at;
  }

  // Create a new user
  static async create(newUser) {
    try {
      // Hash the password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newUser.password, salt);

      const sql = `
        INSERT INTO users (name, email, password, role)
        VALUES (?, ?, ?, ?)
      `;
      
      const result = await db.query(sql, [
        newUser.name,
        newUser.email,
        hashedPassword,
        newUser.role || 'user'
      ]);

      return { id: result.insertId, ...newUser, password: undefined };
    } catch (error) {
      throw error;
    }
  }

  // Find user by ID
  static async findById(id) {
    try {
      const sql = 'SELECT * FROM users WHERE id = ?';
      const rows = await db.query(sql, [id]);
      
      if (rows.length === 0) {
        return null;
      }
      
      const user = rows[0];
      delete user.password; // Don't return password
      
      return new User(user);
    } catch (error) {
      throw error;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    try {
      const sql = 'SELECT * FROM users WHERE email = ?';
      const rows = await db.query(sql, [email]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return new User(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Update user
  static async update(id, userData) {
    try {
      // If password is being updated, hash it
      if (userData.password) {
        const salt = await bcrypt.genSalt(10);
        userData.password = await bcrypt.hash(userData.password, salt);
      }

      // Build the SQL query dynamically based on provided fields
      const fields = Object.keys(userData)
        .filter(key => key !== 'id') // Exclude id from update
        .map(key => `${key} = ?`);
      
      const values = Object.keys(userData)
        .filter(key => key !== 'id')
        .map(key => userData[key]);
      
      values.push(id); // Add id for WHERE clause
      
      const sql = `
        UPDATE users 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await db.query(sql, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete user
  static async delete(id) {
    try {
      const sql = 'DELETE FROM users WHERE id = ?';
      const result = await db.query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get all users
  static async findAll() {
    try {
      const sql = 'SELECT id, name, email, role, created_at, updated_at FROM users';
      const rows = await db.query(sql);
      return rows.map(row => new User(row));
    } catch (error) {
      throw error;
    }
  }

  // Compare password
  async comparePassword(password) {
    return bcrypt.compare(password, this.password);
  }
}

module.exports = User;
