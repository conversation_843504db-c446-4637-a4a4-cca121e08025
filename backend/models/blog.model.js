const db = require('../config/db.config');

class Blog {
  constructor(blog) {
    this.id = blog.id;
    this.title = blog.title;
    this.content = blog.content;
    this.image = blog.image;
    this.author_id = blog.author_id;
    this.author_name = blog.author_name;
    this.status = blog.status || 'published';
    this.created_at = blog.created_at;
    this.updated_at = blog.updated_at;
  }

  // Create a new blog post
  static async create(newBlog) {
    try {
      const sql = `
        INSERT INTO blogs (title, content, image, author_id, status)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      const result = await db.query(sql, [
        newBlog.title,
        newBlog.content,
        newBlog.image,
        newBlog.author_id,
        newBlog.status || 'published'
      ]);

      return { id: result.insertId, ...newBlog };
    } catch (error) {
      throw error;
    }
  }

  // Find blog post by ID
  static async findById(id) {
    try {
      const sql = `
        SELECT b.*, u.name as author_name
        FROM blogs b
        JOIN users u ON b.author_id = u.id
        WHERE b.id = ?
      `;
      const rows = await db.query(sql, [id]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return new Blog(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Update blog post
  static async update(id, blogData) {
    try {
      // Build the SQL query dynamically based on provided fields
      const fields = Object.keys(blogData)
        .filter(key => key !== 'id' && key !== 'author_name') // Exclude id and author_name from update
        .map(key => `${key} = ?`);
      
      const values = Object.keys(blogData)
        .filter(key => key !== 'id' && key !== 'author_name')
        .map(key => blogData[key]);
      
      values.push(id); // Add id for WHERE clause
      
      const sql = `
        UPDATE blogs 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await db.query(sql, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete blog post
  static async delete(id) {
    try {
      const sql = 'DELETE FROM blogs WHERE id = ?';
      const result = await db.query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get all blog posts
  static async findAll(filters = {}) {
    try {
      let sql = `
        SELECT b.*, u.name as author_name
        FROM blogs b
        JOIN users u ON b.author_id = u.id
      `;
      const values = [];
      
      // Add WHERE clauses for filters
      const whereClauses = [];
      
      if (filters.status) {
        whereClauses.push('b.status = ?');
        values.push(filters.status);
      } else {
        // By default, only show published posts
        whereClauses.push('b.status = ?');
        values.push('published');
      }
      
      if (filters.author_id) {
        whereClauses.push('b.author_id = ?');
        values.push(filters.author_id);
      }
      
      if (filters.search) {
        whereClauses.push('(b.title LIKE ? OR b.content LIKE ?)');
        values.push(`%${filters.search}%`);
        values.push(`%${filters.search}%`);
      }
      
      if (whereClauses.length > 0) {
        sql += ' WHERE ' + whereClauses.join(' AND ');
      }
      
      // Add sorting
      if (filters.sortBy) {
        const sortDirection = filters.sortDir === 'desc' ? 'DESC' : 'ASC';
        sql += ` ORDER BY b.${filters.sortBy} ${sortDirection}`;
      } else {
        sql += ' ORDER BY b.created_at DESC';
      }
      
      // Add pagination
      if (filters.limit) {
        sql += ' LIMIT ?';
        values.push(parseInt(filters.limit));
        
        if (filters.offset) {
          sql += ' OFFSET ?';
          values.push(parseInt(filters.offset));
        }
      }
      
      const rows = await db.query(sql, values);
      return rows.map(row => new Blog(row));
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Blog;
