const db = require('../config/db.config');

class Product {
  constructor(product) {
    this.id = product.id;
    this.name = product.name;
    this.description = product.description;
    this.price = product.price;
    this.image = product.image;
    this.category = product.category;
    this.stock = product.stock;
    this.created_at = product.created_at;
    this.updated_at = product.updated_at;
  }

  // Create a new product
  static async create(newProduct) {
    try {
      const sql = `
        INSERT INTO products (name, description, price, image, category, stock)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      
      const result = await db.query(sql, [
        newProduct.name,
        newProduct.description,
        newProduct.price,
        newProduct.image,
        newProduct.category,
        newProduct.stock
      ]);

      return { id: result.insertId, ...newProduct };
    } catch (error) {
      throw error;
    }
  }

  // Find product by ID
  static async findById(id) {
    try {
      const sql = 'SELECT * FROM products WHERE id = ?';
      const rows = await db.query(sql, [id]);
      
      if (rows.length === 0) {
        return null;
      }
      
      return new Product(rows[0]);
    } catch (error) {
      throw error;
    }
  }

  // Update product
  static async update(id, productData) {
    try {
      // Build the SQL query dynamically based on provided fields
      const fields = Object.keys(productData)
        .filter(key => key !== 'id') // Exclude id from update
        .map(key => `${key} = ?`);
      
      const values = Object.keys(productData)
        .filter(key => key !== 'id')
        .map(key => productData[key]);
      
      values.push(id); // Add id for WHERE clause
      
      const sql = `
        UPDATE products 
        SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await db.query(sql, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete product
  static async delete(id) {
    try {
      const sql = 'DELETE FROM products WHERE id = ?';
      const result = await db.query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Get all products
  static async findAll(filters = {}) {
    try {
      let sql = 'SELECT * FROM products';
      const values = [];
      
      // Add WHERE clauses for filters
      if (Object.keys(filters).length > 0) {
        const whereClauses = [];
        
        if (filters.category) {
          whereClauses.push('category = ?');
          values.push(filters.category);
        }
        
        if (filters.minPrice) {
          whereClauses.push('price >= ?');
          values.push(filters.minPrice);
        }
        
        if (filters.maxPrice) {
          whereClauses.push('price <= ?');
          values.push(filters.maxPrice);
        }
        
        if (filters.search) {
          whereClauses.push('(name LIKE ? OR description LIKE ?)');
          values.push(`%${filters.search}%`);
          values.push(`%${filters.search}%`);
        }
        
        if (whereClauses.length > 0) {
          sql += ' WHERE ' + whereClauses.join(' AND ');
        }
      }
      
      // Add sorting
      if (filters.sortBy) {
        const sortDirection = filters.sortDir === 'desc' ? 'DESC' : 'ASC';
        sql += ` ORDER BY ${filters.sortBy} ${sortDirection}`;
      } else {
        sql += ' ORDER BY created_at DESC';
      }
      
      // Add pagination
      if (filters.limit) {
        sql += ' LIMIT ?';
        values.push(parseInt(filters.limit));
        
        if (filters.offset) {
          sql += ' OFFSET ?';
          values.push(parseInt(filters.offset));
        }
      }
      
      const rows = await db.query(sql, values);
      return rows.map(row => new Product(row));
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Product;
