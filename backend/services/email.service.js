const nodemailer = require('nodemailer');
const { loadEmailTemplate, formatEmailAddress } = require('../utils/email.utils');
const logger = require('../utils/logger');
const { 
  EMAIL_HOST, 
  EMAIL_PORT, 
  EMAIL_USER, 
  EMAIL_PASS, 
  EMAIL_FROM 
} = require('../config/env.config');

// Create reusable transporter object using SMTP transport
const transporter = nodemailer.createTransport({
  host: EMAIL_HOST,
  port: EMAIL_PORT,
  secure: EMAIL_PORT === 465, // true for 465, false for other ports
  auth: {
    user: EMAIL_USER,
    pass: EMAIL_PASS
  }
});

/**
 * Send an email
 * @param {Object} options - Email options
 * @param {string} options.to - Recipient email
 * @param {string} options.subject - Email subject
 * @param {string} options.html - Email HTML content
 * @param {string} [options.text] - Email text content (fallback)
 * @param {string} [options.from] - Sender email (defaults to EMAIL_FROM)
 * @param {string} [options.toName] - Recipient name
 * @param {string} [options.fromName] - Sender name
 * @returns {Promise<Object>} - Nodemailer send result
 */
const sendEmail = async (options) => {
  try {
    // Format sender and recipient addresses
    const from = formatEmailAddress(
      options.from || EMAIL_FROM,
      options.fromName
    );
    
    const to = formatEmailAddress(
      options.to,
      options.toName
    );
    
    // Send mail with defined transport object
    const info = await transporter.sendMail({
      from,
      to,
      subject: options.subject,
      text: options.text || '',
      html: options.html
    });
    
    logger.info(`Email sent: ${info.messageId}`);
    return info;
  } catch (error) {
    logger.error(`Error sending email: ${error.message}`);
    throw error;
  }
};

/**
 * Send a welcome email to a new user
 * @param {Object} user - User object
 * @returns {Promise<Object>} - Nodemailer send result
 */
const sendWelcomeEmail = async (user) => {
  try {
    // Load welcome email template
    const html = await loadEmailTemplate('welcome', {
      name: user.name,
      email: user.email,
      appName: 'React Backend App'
    });
    
    // Send the email
    return await sendEmail({
      to: user.email,
      toName: user.name,
      subject: 'Welcome to React Backend App',
      html
    });
  } catch (error) {
    logger.error(`Error sending welcome email: ${error.message}`);
    throw error;
  }
};

/**
 * Send a password reset email
 * @param {Object} user - User object
 * @param {string} resetToken - Password reset token
 * @param {string} resetUrl - Password reset URL
 * @returns {Promise<Object>} - Nodemailer send result
 */
const sendPasswordResetEmail = async (user, resetToken, resetUrl) => {
  try {
    // Load password reset email template
    const html = await loadEmailTemplate('resetPassword', {
      name: user.name,
      resetUrl,
      token: resetToken,
      expiryTime: '1 hour'
    });
    
    // Send the email
    return await sendEmail({
      to: user.email,
      toName: user.name,
      subject: 'Password Reset Request',
      html
    });
  } catch (error) {
    logger.error(`Error sending password reset email: ${error.message}`);
    throw error;
  }
};

/**
 * Send a booking confirmation email
 * @param {Object} booking - Booking object with user and product details
 * @returns {Promise<Object>} - Nodemailer send result
 */
const sendBookingConfirmationEmail = async (booking) => {
  try {
    // Load booking confirmation email template
    const html = await loadEmailTemplate('bookingConfirmation', {
      name: booking.user_name,
      productName: booking.product_name,
      quantity: booking.quantity,
      totalPrice: booking.total_price,
      bookingDate: new Date(booking.booking_date).toLocaleDateString(),
      bookingId: booking.id
    });
    
    // Send the email
    return await sendEmail({
      to: booking.user_email,
      toName: booking.user_name,
      subject: 'Booking Confirmation',
      html
    });
  } catch (error) {
    logger.error(`Error sending booking confirmation email: ${error.message}`);
    throw error;
  }
};

module.exports = {
  sendEmail,
  sendWelcomeEmail,
  sendPasswordResetEmail,
  sendBookingConfirmationEmail
};
