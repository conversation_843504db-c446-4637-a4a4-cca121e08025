const { 
  generateTransactionId, 
  verifyPaymentSignature 
} = require('../utils/payment.utils');
const logger = require('../utils/logger');
const { PAYMENT_API_KEY } = require('../config/env.config');
const Payment = require('../models/payment.model');
const Booking = require('../models/booking.model');

/**
 * Create a payment intent/order
 * @param {Object} paymentData - Payment data
 * @returns {Promise<Object>} - Payment intent/order details
 */
const createPaymentIntent = async (paymentData) => {
  try {
    // Generate a unique transaction ID
    const transactionId = generateTransactionId();
    
    // In a real implementation, you would call your payment gateway API here
    // This is a mock implementation
    const paymentIntent = {
      id: transactionId,
      amount: paymentData.amount,
      currency: paymentData.currency || 'USD',
      status: 'created',
      client_secret: `${transactionId}_secret_${Date.now()}`,
      payment_method_types: ['card'],
      created_at: new Date().toISOString()
    };
    
    logger.info(`Payment intent created: ${transactionId}`);
    return paymentIntent;
  } catch (error) {
    logger.error(`Error creating payment intent: ${error.message}`);
    throw error;
  }
};

/**
 * Process a payment
 * @param {Object} paymentData - Payment data
 * @returns {Promise<Object>} - Payment result
 */
const processPayment = async (paymentData) => {
  try {
    // In a real implementation, you would call your payment gateway API here
    // This is a mock implementation
    const paymentResult = {
      transaction_id: paymentData.transaction_id || generateTransactionId(),
      amount: paymentData.amount,
      currency: paymentData.currency || 'USD',
      status: 'succeeded',
      payment_method: paymentData.payment_method,
      payment_method_details: {
        type: paymentData.payment_method,
        card: {
          last4: '4242',
          brand: 'visa',
          exp_month: 12,
          exp_year: 2025
        }
      },
      created_at: new Date().toISOString()
    };
    
    // Create a payment record in the database
    const payment = await Payment.create({
      booking_id: paymentData.booking_id,
      user_id: paymentData.user_id,
      amount: paymentData.amount,
      currency: paymentData.currency || 'USD',
      payment_method: paymentData.payment_method,
      transaction_id: paymentResult.transaction_id,
      status: paymentResult.status
    });
    
    // Update the booking status
    if (paymentData.booking_id) {
      await Booking.update(paymentData.booking_id, {
        status: 'confirmed',
        payment_id: payment.id
      });
    }
    
    logger.info(`Payment processed: ${paymentResult.transaction_id}`);
    return { payment, paymentResult };
  } catch (error) {
    logger.error(`Error processing payment: ${error.message}`);
    throw error;
  }
};

/**
 * Verify a payment webhook
 * @param {Object} webhookData - Webhook data from payment gateway
 * @param {string} signature - Webhook signature
 * @returns {Promise<Object>} - Verified webhook data
 */
const verifyPaymentWebhook = async (webhookData, signature) => {
  try {
    // Verify the webhook signature
    const isValid = verifyPaymentSignature(webhookData, signature);
    
    if (!isValid) {
      throw new Error('Invalid webhook signature');
    }
    
    // Process the webhook based on event type
    switch (webhookData.type) {
      case 'payment_intent.succeeded':
        // Update payment status in database
        const payment = await Payment.findByTransactionId(webhookData.data.id);
        if (payment) {
          await Payment.update(payment.id, { status: 'succeeded' });
          
          // Update booking status if applicable
          if (payment.booking_id) {
            await Booking.update(payment.booking_id, { status: 'confirmed' });
          }
        }
        break;
        
      case 'payment_intent.payment_failed':
        // Update payment status in database
        const failedPayment = await Payment.findByTransactionId(webhookData.data.id);
        if (failedPayment) {
          await Payment.update(failedPayment.id, { status: 'failed' });
        }
        break;
        
      // Handle other webhook events as needed
    }
    
    logger.info(`Payment webhook processed: ${webhookData.type}`);
    return { success: true, event: webhookData.type };
  } catch (error) {
    logger.error(`Error verifying payment webhook: ${error.message}`);
    throw error;
  }
};

module.exports = {
  createPaymentIntent,
  processPayment,
  verifyPaymentWebhook
};
