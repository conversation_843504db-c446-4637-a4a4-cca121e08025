const express = require('express');
const { body } = require('express-validator');
const router = express.Router();
const blogController = require('../controllers/blog.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const { validationErrorHandler } = require('../middleware/error.middleware');
const multer = require('multer');
const path = require('path');
const { UPLOAD_PATH } = require('../config/env.config');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, UPLOAD_PATH);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'blog-' + uniqueSuffix + ext);
  }
});

const fileFilter = (req, file, cb) => {
  // Accept only images
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Only image files are allowed!'), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB
  }
});

// Create a new blog post (authenticated users)
router.post(
  '/',
  verifyToken,
  upload.single('image'),
  [
    body('title').notEmpty().withMessage('Title is required'),
    body('content').notEmpty().withMessage('Content is required'),
    body('status').optional().isIn(['published', 'draft']).withMessage('Status must be either published or draft')
  ],
  validationErrorHandler,
  blogController.createBlog
);

// Get all blog posts (public)
router.get('/', blogController.getAllBlogs);

// Get blog post by ID (public for published, auth for drafts)
router.get('/:id', blogController.getBlogById);

// Update blog post (author or admin)
router.put(
  '/:id',
  verifyToken,
  upload.single('image'),
  [
    body('title').optional().notEmpty().withMessage('Title cannot be empty'),
    body('content').optional().notEmpty().withMessage('Content cannot be empty'),
    body('status').optional().isIn(['published', 'draft']).withMessage('Status must be either published or draft')
  ],
  validationErrorHandler,
  blogController.updateBlog
);

// Delete blog post (author or admin)
router.delete(
  '/:id',
  verifyToken,
  blogController.deleteBlog
);

module.exports = router;
