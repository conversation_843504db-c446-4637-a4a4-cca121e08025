const express = require('express');
const { body } = require('express-validator');
const router = express.Router();
const bookingController = require('../controllers/booking.controller');
const { verifyToken, isAdmin } = require('../middleware/auth.middleware');
const { validationErrorHandler } = require('../middleware/error.middleware');

// Create a new booking (authenticated users)
router.post(
  '/',
  verifyToken,
  [
    body('product_id').notEmpty().withMessage('Product ID is required'),
    body('quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
    body('booking_date').optional().isISO8601().withMessage('Booking date must be a valid date')
  ],
  validationErrorHandler,
  bookingController.createBooking
);

// Get all bookings (authenticated users - own bookings, admin - all bookings)
router.get(
  '/',
  verifyToken,
  bookingController.getAllBookings
);

// Get booking by ID (owner or admin)
router.get(
  '/:id',
  verifyToken,
  bookingController.getBookingById
);

// Update booking status (owner or admin)
router.patch(
  '/:id/status',
  verifyToken,
  [
    body('status')
      .isIn(['pending', 'confirmed', 'cancelled', 'completed'])
      .withMessage('Status must be one of: pending, confirmed, cancelled, completed')
  ],
  validationErrorHandler,
  bookingController.updateBookingStatus
);

// Delete booking (admin only)
router.delete(
  '/:id',
  verifyToken,
  isAdmin,
  bookingController.deleteBooking
);

module.exports = router;
