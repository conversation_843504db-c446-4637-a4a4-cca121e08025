const express = require('express');
const { body } = require('express-validator');
const router = express.Router();
const paymentController = require('../controllers/payment.controller');
const { verifyToken, isAdmin } = require('../middleware/auth.middleware');
const { validationErrorHandler } = require('../middleware/error.middleware');

// Create payment intent (authenticated users)
router.post(
  '/create-intent',
  verifyToken,
  [
    body('booking_id').notEmpty().withMessage('Booking ID is required'),
    body('payment_method').optional().isIn(['card', 'paypal']).withMessage('Payment method must be card or paypal')
  ],
  validationErrorHandler,
  paymentController.createPaymentIntentController
);

// Process payment (authenticated users)
router.post(
  '/process',
  verifyToken,
  [
    body('booking_id').notEmpty().withMessage('Booking ID is required'),
    body('payment_method').notEmpty().withMessage('Payment method is required'),
    body('transaction_id').optional()
  ],
  validationError<PERSON>and<PERSON>,
  paymentController.processPaymentController
);

// Get all payments (authenticated users - own payments, admin - all payments)
router.get(
  '/',
  verifyToken,
  paymentController.getAllPayments
);

// Get payment by ID (owner or admin)
router.get(
  '/:id',
  verifyToken,
  paymentController.getPaymentById
);

// Payment webhook (no auth - secured by signature)
router.post(
  '/webhook',
  paymentController.paymentWebhook
);

module.exports = router;
