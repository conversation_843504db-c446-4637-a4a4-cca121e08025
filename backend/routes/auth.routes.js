const express = require('express');
const { body } = require('express-validator');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const { verifyToken } = require('../middleware/auth.middleware');
const { validationError<PERSON>andler } = require('../middleware/error.middleware');

// Register a new user
router.post(
  '/register',
  [
    body('name').notEmpty().withMessage('Name is required'),
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long')
  ],
  validationErrorHandler,
  authController.register
);

// Login user
router.post(
  '/login',
  [
    body('email').isEmail().withMessage('Please provide a valid email'),
    body('password').notEmpty().withMessage('Password is required')
  ],
  validation<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  authController.login
);

// Get current user profile
router.get(
  '/profile',
  verifyToken,
  authController.getProfile
);

// Update user profile
router.put(
  '/profile',
  verifyToken,
  [
    body('name').optional().notEmpty().withMessage('Name cannot be empty'),
    body('email').optional().isEmail().withMessage('Please provide a valid email')
  ],
  validationErrorHandler,
  authController.updateProfile
);

// Change password
router.post(
  '/change-password',
  verifyToken,
  [
    body('currentPassword').notEmpty().withMessage('Current password is required'),
    body('newPassword')
      .isLength({ min: 6 })
      .withMessage('New password must be at least 6 characters long')
  ],
  validationErrorHandler,
  authController.changePassword
);

// Request password reset
router.post(
  '/request-reset',
  [
    body('email').isEmail().withMessage('Please provide a valid email')
  ],
  validationErrorHandler,
  authController.requestPasswordReset
);

// Reset password
router.post(
  '/reset-password',
  [
    body('token').notEmpty().withMessage('Token is required'),
    body('password')
      .isLength({ min: 6 })
      .withMessage('Password must be at least 6 characters long')
  ],
  validationErrorHandler,
  authController.resetPassword
);

module.exports = router;
