const logger = require('../utils/logger');
const { validationResult } = require('express-validator');

// Not Found Error Handler
const notFound = (req, res, next) => {
  const error = new Error(`Not Found - ${req.originalUrl}`);
  error.status = 404;
  next(error);
};

// General <PERSON><PERSON><PERSON>
const errorHandler = (err, req, res, next) => {
  // Log the error
  logger.error({
    message: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method,
    ip: req.ip
  });

  // Set status code
  const statusCode = err.status || 500;

  // Send response
  res.status(statusCode).json({
    success: false,
    message: statusCode === 500 ? 'Internal Server Error' : err.message,
    stack: process.env.NODE_ENV === 'production' ? '🥞' : err.stack
  });
};

// Validation Error Handler
const validationErrorHandler = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: errors.array()
    });
  }
  next();
};

// Database Error Handler
const dbErrorHandler = (err, req, res, next) => {
  // Check if it's a database error
  if (err.code && (
    err.code === 'ER_DUP_ENTRY' ||
    err.code === 'ER_NO_REFERENCED_ROW' ||
    err.code === 'ER_ROW_IS_REFERENCED'
  )) {
    let message = 'Database error';

    // Customize message based on error code
    if (err.code === 'ER_DUP_ENTRY') {
      message = 'Duplicate entry. This record already exists.';
    } else if (err.code === 'ER_NO_REFERENCED_ROW') {
      message = 'Referenced record does not exist.';
    } else if (err.code === 'ER_ROW_IS_REFERENCED') {
      message = 'Cannot delete or update a parent row: a foreign key constraint fails.';
    }

    return res.status(400).json({
      success: false,
      message,
      error: err.message
    });
  }

  next(err);
};

module.exports = {
  notFound,
  errorHandler,
  validationErrorHandler,
  dbErrorHandler
};
