const jwt = require('jsonwebtoken');
const { JWT_SECRET } = require('../config/env.config');
const User = require('../models/user.model');

// Middleware to verify JWT token
const verifyToken = (req, res, next) => {
  // Get the token from the request headers
  const authHeader = req.headers.authorization;
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'Access denied. No token provided.'
    });
  }
  
  const token = authHeader.split(' ')[1];
  
  try {
    // Verify the token
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Add the user data to the request object
    req.user = decoded;
    
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token.'
    });
  }
};

// Middleware to check if user is admin
const isAdmin = async (req, res, next) => {
  try {
    // First verify the token
    verifyToken(req, res, async () => {
      // Get the user from the database
      const user = await User.findById(req.user.id);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found.'
        });
      }
      
      // Check if the user is an admin
      if (user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Admin role required.'
        });
      }
      
      next();
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Internal server error.',
      error: error.message
    });
  }
};

// Middleware to check if user is the owner of the resource or an admin
const isOwnerOrAdmin = (model, paramIdField = 'id') => {
  return async (req, res, next) => {
    try {
      // First verify the token
      verifyToken(req, res, async () => {
        // Get the user from the database
        const user = await User.findById(req.user.id);
        
        if (!user) {
          return res.status(404).json({
            success: false,
            message: 'User not found.'
          });
        }
        
        // If user is admin, allow access
        if (user.role === 'admin') {
          return next();
        }
        
        // Get the resource ID from the request parameters
        const resourceId = req.params[paramIdField];
        
        // Get the resource from the database
        const resource = await model.findById(resourceId);
        
        if (!resource) {
          return res.status(404).json({
            success: false,
            message: 'Resource not found.'
          });
        }
        
        // Check if the user is the owner of the resource
        if (resource.user_id !== user.id) {
          return res.status(403).json({
            success: false,
            message: 'Access denied. You are not the owner of this resource.'
          });
        }
        
        next();
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Internal server error.',
        error: error.message
      });
    }
  };
};

module.exports = {
  verifyToken,
  isAdmin,
  isOwnerOrAdmin
};
