const Payment = require('../models/payment.model');
const Booking = require('../models/booking.model');
const { 
  createPaymentIntent, 
  processPayment, 
  verifyPaymentWebhook 
} = require('../services/payment.service');
const logger = require('../utils/logger');

// Create payment intent
const createPaymentIntentController = async (req, res, next) => {
  try {
    const { booking_id, payment_method } = req.body;
    
    // Check if booking exists
    const booking = await Booking.findById(booking_id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }
    
    // Check if user is authorized to pay for this booking
    if (req.user.id !== booking.user_id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not authorized to pay for this booking.'
      });
    }
    
    // Check if booking is already paid
    if (booking.status === 'confirmed' || booking.status === 'completed') {
      return res.status(400).json({
        success: false,
        message: 'This booking is already paid for'
      });
    }
    
    // Create payment intent
    const paymentIntent = await createPaymentIntent({
      amount: booking.total_price,
      currency: 'USD',
      payment_method_types: [payment_method || 'card']
    });
    
    // Return success response
    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntent
    });
  } catch (error) {
    next(error);
  }
};

// Process payment
const processPaymentController = async (req, res, next) => {
  try {
    const { 
      booking_id, 
      payment_method, 
      transaction_id 
    } = req.body;
    
    // Check if booking exists
    const booking = await Booking.findById(booking_id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }
    
    // Check if user is authorized to pay for this booking
    if (req.user.id !== booking.user_id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not authorized to pay for this booking.'
      });
    }
    
    // Process payment
    const { payment, paymentResult } = await processPayment({
      booking_id,
      user_id: req.user.id,
      amount: booking.total_price,
      currency: 'USD',
      payment_method,
      transaction_id
    });
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Payment processed successfully',
      payment,
      paymentResult
    });
  } catch (error) {
    next(error);
  }
};

// Get all payments
const getAllPayments = async (req, res, next) => {
  try {
    // Extract query parameters for filtering, sorting, and pagination
    const {
      status,
      payment_method,
      sortBy,
      sortDir,
      limit,
      page
    } = req.query;
    
    // Calculate offset for pagination
    const offset = page ? (parseInt(page) - 1) * parseInt(limit || 10) : 0;
    
    // Set user_id filter based on role
    let user_id = null;
    if (req.user.role !== 'admin') {
      user_id = req.user.id;
    }
    
    // Get payments with filters
    const payments = await Payment.findAll({
      user_id,
      status,
      payment_method,
      sortBy,
      sortDir,
      limit,
      offset
    });
    
    // Return success response
    res.status(200).json({
      success: true,
      count: payments.length,
      payments
    });
  } catch (error) {
    next(error);
  }
};

// Get payment by ID
const getPaymentById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Get payment
    const payment = await Payment.findById(id);
    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }
    
    // Check if user is authorized to view this payment
    if (req.user.role !== 'admin' && req.user.id !== payment.user_id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not authorized to view this payment.'
      });
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      payment
    });
  } catch (error) {
    next(error);
  }
};

// Payment webhook handler
const paymentWebhook = async (req, res, next) => {
  try {
    const signature = req.headers['x-payment-signature'];
    
    // Verify webhook
    const result = await verifyPaymentWebhook(req.body, signature);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Webhook processed successfully',
      result
    });
  } catch (error) {
    // Don't use next() for webhooks, just log the error and return a 200 response
    // to acknowledge receipt (this is standard practice for webhooks)
    logger.error(`Payment webhook error: ${error.message}`);
    
    res.status(200).json({
      success: false,
      message: 'Webhook received but processing failed'
    });
  }
};

module.exports = {
  createPaymentIntentController,
  processPaymentController,
  getAllPayments,
  getPaymentById,
  paymentWebhook
};
