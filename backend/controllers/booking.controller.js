const Booking = require('../models/booking.model');
const Product = require('../models/product.model');
const { sendBookingConfirmationEmail } = require('../services/email.service');
const logger = require('../utils/logger');

// Create a new booking
const createBooking = async (req, res, next) => {
  try {
    const { product_id, quantity, booking_date } = req.body;
    
    // Check if product exists
    const product = await Product.findById(product_id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Check if product is in stock
    if (product.stock < quantity) {
      return res.status(400).json({
        success: false,
        message: 'Not enough stock available'
      });
    }
    
    // Calculate total price
    const total_price = product.price * quantity;
    
    // Create booking
    const booking = await Booking.create({
      user_id: req.user.id,
      product_id,
      quantity,
      total_price,
      status: 'pending',
      booking_date: booking_date || new Date()
    });
    
    // Update product stock
    await Product.update(product_id, {
      stock: product.stock - quantity
    });
    
    // Get booking with user and product details
    const bookingWithDetails = await Booking.findById(booking.id);
    
    // Send booking confirmation email
    await sendBookingConfirmationEmail(bookingWithDetails);
    
    // Return success response
    res.status(201).json({
      success: true,
      message: 'Booking created successfully',
      booking: bookingWithDetails
    });
  } catch (error) {
    next(error);
  }
};

// Get all bookings
const getAllBookings = async (req, res, next) => {
  try {
    // Extract query parameters for filtering, sorting, and pagination
    const {
      status,
      product_id,
      date_from,
      date_to,
      sortBy,
      sortDir,
      limit,
      page
    } = req.query;
    
    // Calculate offset for pagination
    const offset = page ? (parseInt(page) - 1) * parseInt(limit || 10) : 0;
    
    // Set user_id filter based on role
    let user_id = null;
    if (req.user.role !== 'admin') {
      user_id = req.user.id;
    }
    
    // Get bookings with filters
    const bookings = await Booking.findAll({
      user_id,
      status,
      product_id,
      date_from,
      date_to,
      sortBy,
      sortDir,
      limit,
      offset
    });
    
    // Return success response
    res.status(200).json({
      success: true,
      count: bookings.length,
      bookings
    });
  } catch (error) {
    next(error);
  }
};

// Get booking by ID
const getBookingById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Get booking
    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }
    
    // Check if user is authorized to view this booking
    if (req.user.role !== 'admin' && req.user.id !== booking.user_id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not authorized to view this booking.'
      });
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      booking
    });
  } catch (error) {
    next(error);
  }
};

// Update booking status
const updateBookingStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;
    
    // Check if booking exists
    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }
    
    // Check if user is authorized to update this booking
    if (req.user.role !== 'admin' && req.user.id !== booking.user_id) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not authorized to update this booking.'
      });
    }
    
    // Validate status
    const validStatuses = ['pending', 'confirmed', 'cancelled', 'completed'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Status must be one of: pending, confirmed, cancelled, completed'
      });
    }
    
    // Handle cancellation - return stock
    if (status === 'cancelled' && booking.status !== 'cancelled') {
      const product = await Product.findById(booking.product_id);
      if (product) {
        await Product.update(product.id, {
          stock: product.stock + booking.quantity
        });
      }
    }
    
    // Update booking
    await Booking.update(id, { status });
    
    // Get updated booking
    const updatedBooking = await Booking.findById(id);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Booking status updated successfully',
      booking: updatedBooking
    });
  } catch (error) {
    next(error);
  }
};

// Delete booking
const deleteBooking = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Check if booking exists
    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(404).json({
        success: false,
        message: 'Booking not found'
      });
    }
    
    // Check if user is authorized to delete this booking
    if (req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Only admins can delete bookings.'
      });
    }
    
    // Delete booking
    await Booking.delete(id);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Booking deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createBooking,
  getAllBookings,
  getBookingById,
  updateBookingStatus,
  deleteBooking
};
