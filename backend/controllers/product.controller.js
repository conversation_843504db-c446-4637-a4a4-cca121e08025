const Product = require('../models/product.model');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs').promises;
const { UPLOAD_PATH } = require('../config/env.config');

// Create a new product
const createProduct = async (req, res, next) => {
  try {
    const { name, description, price, category, stock } = req.body;
    
    // Handle image upload
    let image = null;
    if (req.file) {
      image = `/images/${req.file.filename}`;
    }
    
    // Create product
    const product = await Product.create({
      name,
      description,
      price,
      image,
      category,
      stock: stock || 0
    });
    
    // Return success response
    res.status(201).json({
      success: true,
      message: 'Product created successfully',
      product
    });
  } catch (error) {
    next(error);
  }
};

// Get all products
const getAllProducts = async (req, res, next) => {
  try {
    // Extract query parameters for filtering, sorting, and pagination
    const {
      category,
      minPrice,
      maxPrice,
      search,
      sortBy,
      sortDir,
      limit,
      page
    } = req.query;
    
    // Calculate offset for pagination
    const offset = page ? (parseInt(page) - 1) * parseInt(limit || 10) : 0;
    
    // Get products with filters
    const products = await Product.findAll({
      category,
      minPrice,
      maxPrice,
      search,
      sortBy,
      sortDir,
      limit,
      offset
    });
    
    // Return success response
    res.status(200).json({
      success: true,
      count: products.length,
      products
    });
  } catch (error) {
    next(error);
  }
};

// Get product by ID
const getProductById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Get product
    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      product
    });
  } catch (error) {
    next(error);
  }
};

// Update product
const updateProduct = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, price, category, stock } = req.body;
    
    // Check if product exists
    const existingProduct = await Product.findById(id);
    if (!existingProduct) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Handle image upload
    let image = existingProduct.image;
    if (req.file) {
      // Delete old image if exists
      if (existingProduct.image) {
        try {
          const oldImagePath = path.join(UPLOAD_PATH, existingProduct.image.replace('/images/', ''));
          await fs.unlink(oldImagePath);
        } catch (err) {
          logger.error(`Error deleting old image: ${err.message}`);
        }
      }
      
      // Set new image
      image = `/images/${req.file.filename}`;
    }
    
    // Update product
    const productData = {
      name,
      description,
      price,
      image,
      category,
      stock
    };
    
    // Remove undefined fields
    Object.keys(productData).forEach(key => {
      if (productData[key] === undefined) {
        delete productData[key];
      }
    });
    
    await Product.update(id, productData);
    
    // Get updated product
    const updatedProduct = await Product.findById(id);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Product updated successfully',
      product: updatedProduct
    });
  } catch (error) {
    next(error);
  }
};

// Delete product
const deleteProduct = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Check if product exists
    const product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }
    
    // Delete product image if exists
    if (product.image) {
      try {
        const imagePath = path.join(UPLOAD_PATH, product.image.replace('/images/', ''));
        await fs.unlink(imagePath);
      } catch (err) {
        logger.error(`Error deleting image: ${err.message}`);
      }
    }
    
    // Delete product
    await Product.delete(id);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Product deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createProduct,
  getAllProducts,
  getProductById,
  updateProduct,
  deleteProduct
};
