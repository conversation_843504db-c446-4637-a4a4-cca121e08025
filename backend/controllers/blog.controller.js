const Blog = require('../models/blog.model');
const logger = require('../utils/logger');
const path = require('path');
const fs = require('fs').promises;
const { UPLOAD_PATH } = require('../config/env.config');

// Create a new blog post
const createBlog = async (req, res, next) => {
  try {
    const { title, content, status } = req.body;
    
    // Handle image upload
    let image = null;
    if (req.file) {
      image = `/images/${req.file.filename}`;
    }
    
    // Create blog post
    const blog = await Blog.create({
      title,
      content,
      image,
      author_id: req.user.id,
      status: status || 'published'
    });
    
    // Return success response
    res.status(201).json({
      success: true,
      message: 'Blog post created successfully',
      blog
    });
  } catch (error) {
    next(error);
  }
};

// Get all blog posts
const getAllBlogs = async (req, res, next) => {
  try {
    // Extract query parameters for filtering, sorting, and pagination
    const {
      status,
      author_id,
      search,
      sortBy,
      sortDir,
      limit,
      page
    } = req.query;
    
    // Calculate offset for pagination
    const offset = page ? (parseInt(page) - 1) * parseInt(limit || 10) : 0;
    
    // Get blog posts with filters
    const blogs = await Blog.findAll({
      status,
      author_id,
      search,
      sortBy,
      sortDir,
      limit,
      offset
    });
    
    // Return success response
    res.status(200).json({
      success: true,
      count: blogs.length,
      blogs
    });
  } catch (error) {
    next(error);
  }
};

// Get blog post by ID
const getBlogById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Get blog post
    const blog = await Blog.findById(id);
    if (!blog) {
      return res.status(404).json({
        success: false,
        message: 'Blog post not found'
      });
    }
    
    // Check if blog post is published or user is author/admin
    if (blog.status !== 'published' && 
        (!req.user || (req.user.id !== blog.author_id && req.user.role !== 'admin'))) {
      return res.status(403).json({
        success: false,
        message: 'Access denied. This blog post is not published.'
      });
    }
    
    // Return success response
    res.status(200).json({
      success: true,
      blog
    });
  } catch (error) {
    next(error);
  }
};

// Update blog post
const updateBlog = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { title, content, status } = req.body;
    
    // Check if blog post exists
    const existingBlog = await Blog.findById(id);
    if (!existingBlog) {
      return res.status(404).json({
        success: false,
        message: 'Blog post not found'
      });
    }
    
    // Check if user is author or admin
    if (req.user.id !== existingBlog.author_id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not the author of this blog post.'
      });
    }
    
    // Handle image upload
    let image = existingBlog.image;
    if (req.file) {
      // Delete old image if exists
      if (existingBlog.image) {
        try {
          const oldImagePath = path.join(UPLOAD_PATH, existingBlog.image.replace('/images/', ''));
          await fs.unlink(oldImagePath);
        } catch (err) {
          logger.error(`Error deleting old image: ${err.message}`);
        }
      }
      
      // Set new image
      image = `/images/${req.file.filename}`;
    }
    
    // Update blog post
    const blogData = {
      title,
      content,
      image,
      status
    };
    
    // Remove undefined fields
    Object.keys(blogData).forEach(key => {
      if (blogData[key] === undefined) {
        delete blogData[key];
      }
    });
    
    await Blog.update(id, blogData);
    
    // Get updated blog post
    const updatedBlog = await Blog.findById(id);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Blog post updated successfully',
      blog: updatedBlog
    });
  } catch (error) {
    next(error);
  }
};

// Delete blog post
const deleteBlog = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    // Check if blog post exists
    const blog = await Blog.findById(id);
    if (!blog) {
      return res.status(404).json({
        success: false,
        message: 'Blog post not found'
      });
    }
    
    // Check if user is author or admin
    if (req.user.id !== blog.author_id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. You are not the author of this blog post.'
      });
    }
    
    // Delete blog image if exists
    if (blog.image) {
      try {
        const imagePath = path.join(UPLOAD_PATH, blog.image.replace('/images/', ''));
        await fs.unlink(imagePath);
      } catch (err) {
        logger.error(`Error deleting image: ${err.message}`);
      }
    }
    
    // Delete blog post
    await Blog.delete(id);
    
    // Return success response
    res.status(200).json({
      success: true,
      message: 'Blog post deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createBlog,
  getAllBlogs,
  getBlogById,
  updateBlog,
  deleteBlog
};
